# Kawaii Chat Application Requirements
# Core web framework
Flask==2.3.3
Werkzeug==2.3.7

# Real-time communication
Flask-SocketIO==5.3.6
python-socketio==5.8.0
python-engineio==4.7.1

# Environment variables
python-dotenv==1.0.0

# Image processing
Pillow==10.0.1

# Cloud storage (Cloudinary)
cloudinary==1.36.0

# Database (if you want to add PostgreSQL support later)
# psycopg2-binary==2.9.7

# Additional useful packages
requests==2.31.0

# Development and testing (optional)
# pytest==7.4.2
# pytest-flask==1.2.0

# Production server (optional)
# gunicorn==21.2.0
# eventlet==0.33.3
