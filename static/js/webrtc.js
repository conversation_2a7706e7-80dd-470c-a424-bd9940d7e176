// WebRTC Call Manager for Kawaii Chat
class WebRTCCallManager {
    constructor() {
        this.localStream = null;
        this.remoteStream = null;
        this.peerConnection = null;
        this.currentCall = null;
        this.isCallActive = false;
        this.isMinimized = false;
        this.callStartTime = null;
        this.callTimer = null;
        this.isMuted = false;
        this.isCameraOff = false;
        
        // WebRTC Configuration
        this.pcConfig = {
            iceServers: [
                { urls: 'stun:stun.l.google.com:19302' },
                { urls: 'stun:stun1.l.google.com:19302' },
                { urls: 'stun:stun2.l.google.com:19302' }
            ]
        };
        
        this.init();
    }
    
    init() {
        this.checkMediaSupport();
        this.waitForSocketAndBind();
        console.log('📞 WebRTC Call Manager initialized');
    }

    waitForSocketAndBind() {
        if (window.socket && window.socket.connected) {
            console.log('✅ Socket ready, binding events immediately');
            this.bindSocketEvents();
        } else {
            console.log('⏳ Waiting for socket connection...');
            const checkSocket = () => {
                if (window.socket && window.socket.connected) {
                    console.log('✅ Socket connected, binding WebRTC events');
                    this.bindSocketEvents();
                } else {
                    setTimeout(checkSocket, 100);
                }
            };
            checkSocket();
        }
    }

    checkMediaSupport() {
        // Check if WebRTC is supported
        if (!window.RTCPeerConnection) {
            console.error('❌ WebRTC is not supported in this browser');
            return false;
        }

        // Check if getUserMedia is supported
        if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
            console.error('❌ getUserMedia is not supported in this browser');
            return false;
        }

        console.log('✅ WebRTC and getUserMedia are supported');
        return true;
    }

    async checkMediaPermissions() {
        try {
            // Check if permissions API is available
            if (navigator.permissions) {
                const micPermission = await navigator.permissions.query({ name: 'microphone' });
                const cameraPermission = await navigator.permissions.query({ name: 'camera' });

                console.log('🎤 Microphone permission:', micPermission.state);
                console.log('📷 Camera permission:', cameraPermission.state);

                return {
                    microphone: micPermission.state,
                    camera: cameraPermission.state
                };
            }
        } catch (error) {
            console.log('ℹ️ Permissions API not available:', error);
        }

        return null;
    }

    async checkPermissionStatus() {
        console.log('🔍 Checking current permission status...');

        const status = {
            secure: window.isSecureContext,
            protocol: location.protocol,
            host: location.host,
            getUserMediaSupported: !!(navigator.mediaDevices && navigator.mediaDevices.getUserMedia),
            webrtcSupported: !!window.RTCPeerConnection,
            permissions: {}
        };

        // Check permissions API if available
        if (navigator.permissions) {
            try {
                const micPermission = await navigator.permissions.query({ name: 'microphone' });
                status.permissions.microphone = micPermission.state;

                try {
                    const cameraPermission = await navigator.permissions.query({ name: 'camera' });
                    status.permissions.camera = cameraPermission.state;
                } catch (e) {
                    status.permissions.camera = 'unknown';
                }
            } catch (e) {
                status.permissions.microphone = 'unknown';
                status.permissions.camera = 'unknown';
            }
        }

        console.log('📋 Permission status:', status);
        return status;
    }

    async requestMediaPermissions(includeVideo = false) {
        try {
            console.log('🔐 Requesting media permissions...');
            console.log('Current URL:', location.href);
            console.log('Protocol:', location.protocol);
            console.log('Is secure context:', window.isSecureContext);

            // Check if we're in a secure context
            if (!window.isSecureContext) {
                throw new Error('SECURITY_ERROR: Media access requires HTTPS or localhost. Current URL: ' + location.href);
            }

            // Check if getUserMedia is available
            if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
                throw new Error('BROWSER_ERROR: Your browser does not support media access. Please use Chrome, Firefox, Safari, or Edge.');
            }

            // Check available devices first
            let actualIncludeVideo = includeVideo;
            try {
                const devices = await navigator.mediaDevices.enumerateDevices();
                const videoInputs = devices.filter(d => d.kind === 'videoinput');
                const audioInputs = devices.filter(d => d.kind === 'audioinput');

                console.log(`🎤 Audio devices: ${audioInputs.length}`);
                console.log(`📷 Video devices: ${videoInputs.length}`);

                if (includeVideo && videoInputs.length === 0) {
                    console.warn('📷 No camera found, requesting audio permission only');
                    actualIncludeVideo = false;
                    this.showCallError('📷 No camera found, requesting microphone permission only...', 'info');
                } else if (audioInputs.length === 0) {
                    throw new Error('HARDWARE_ERROR: No microphone found. Please connect a microphone.');
                }
            } catch (deviceError) {
                console.warn('Could not check devices:', deviceError);
            }

            // Show user-friendly message
            const message = actualIncludeVideo ?
                '🎤📷 Requesting microphone and camera permission... Please click "Allow" when prompted.' :
                '🎤 Requesting microphone permission... Please click "Allow" when prompted.';
            this.showCallError(message, 'info');

            // Use very basic constraints to avoid issues
            const constraints = {
                audio: {
                    echoCancellation: false,
                    noiseSuppression: false,
                    autoGainControl: false
                },
                video: actualIncludeVideo ? {
                    width: 320,
                    height: 240
                } : false
            };

            console.log('📱 Requesting with constraints:', constraints);

            // Add timeout to the permission request
            const permissionTimeout = new Promise((_, reject) => {
                setTimeout(() => reject(new Error('TIMEOUT_ERROR: Permission request timed out. Please try again.')), 30000);
            });

            const mediaRequest = navigator.mediaDevices.getUserMedia(constraints);

            // Race between permission request and timeout
            const stream = await Promise.race([mediaRequest, permissionTimeout]);

            console.log('✅ Permissions granted successfully');
            console.log('Stream tracks:', stream.getTracks().map(t => `${t.kind}: ${t.label}`));

            // Stop the stream immediately - we just wanted permissions
            stream.getTracks().forEach(track => {
                console.log(`Stopping ${track.kind} track:`, track.label);
                track.stop();
            });

            // Clear any existing error messages
            setTimeout(() => {
                const toastContainer = document.getElementById('toastContainer');
                if (toastContainer) {
                    toastContainer.innerHTML = '';
                }
            }, 1000);

            this.showCallError('✅ Permissions granted! You can now make calls.', 'success');
            return true;

        } catch (error) {
            console.error('❌ Permission request failed:', error);
            console.error('Error name:', error.name);
            console.error('Error message:', error.message);

            let errorMessage = '';
            let helpMessage = '';

            if (error.message.includes('SECURITY_ERROR')) {
                errorMessage = '🔒 Security Error: This site must use HTTPS for microphone access.';
                helpMessage = `\n\nCurrent URL: ${location.href}\n\n🔧 Solutions:\n1. Use https:// instead of http://\n2. Or access via localhost (http://localhost:5000)\n3. Or use 127.0.0.1 (http://127.0.0.1:5000)`;
            } else if (error.message.includes('BROWSER_ERROR')) {
                errorMessage = '🌐 Browser Error: Your browser doesn\'t support media access.';
                helpMessage = '\n\n🔧 Please use:\n• Chrome 53+\n• Firefox 36+\n• Safari 11+\n• Edge 12+';
            } else if (error.message.includes('TIMEOUT_ERROR')) {
                errorMessage = '⏰ Timeout: Permission request took too long.';
                helpMessage = '\n\n🔧 Try again and click "Allow" quickly when prompted.';
            } else if (error.name === 'NotAllowedError') {
                errorMessage = '❌ Permission Denied: You blocked microphone access.';
                helpMessage = '\n\n🔧 To fix this:\n\n1. Look for 🎤 or 🔒 icon in your browser address bar\n2. Click it and select "Allow"\n3. Refresh this page\n4. Try again\n\nOr:\n• Chrome: Settings → Privacy → Site Settings → Microphone\n• Firefox: Settings → Privacy → Permissions → Microphone\n• Safari: Preferences → Websites → Microphone';
            } else if (error.name === 'NotFoundError') {
                errorMessage = '🎤 No Microphone Found: Cannot detect audio input device.';
                helpMessage = '\n\n🔧 Please check:\n• Microphone is connected\n• Microphone is not muted\n• Other apps aren\'t using it\n• Try unplugging and reconnecting';
            } else if (error.name === 'NotReadableError') {
                errorMessage = '🔒 Device Busy: Microphone is being used by another application.';
                helpMessage = '\n\n🔧 Please:\n• Close other video/audio apps\n• Close other browser tabs using microphone\n• Restart your browser\n• Try again';
            } else if (error.name === 'OverconstrainedError') {
                errorMessage = '⚙️ Device Constraints: Your microphone doesn\'t support the requested settings.';
                helpMessage = '\n\n🔧 This is usually a temporary issue. Try refreshing the page.';
            } else if (error.name === 'SecurityError') {
                errorMessage = '🔒 Security Error: Media access blocked by browser security.';
                helpMessage = '\n\n🔧 Make sure you\'re using:\n• HTTPS (https://)\n• Or localhost (http://localhost:5000)\n• Or 127.0.0.1 (http://127.0.0.1:5000)';
            } else {
                errorMessage = '❌ Unknown Error: ' + error.message;
                helpMessage = '\n\n🔧 Try:\n• Refreshing the page\n• Restarting your browser\n• Checking browser console for details';
            }

            this.showCallError(errorMessage + helpMessage);
            return false;
        }
    }

    async preflightCheck(callType = 'audio') {
        console.log('🛫 Running preflight check for', callType, 'call...');

        const issues = [];

        // Check secure context
        if (!window.isSecureContext) {
            issues.push({
                type: 'security',
                message: 'HTTPS required for media access',
                fix: 'Use https:// or localhost'
            });
        }

        // Check browser support
        if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
            issues.push({
                type: 'browser',
                message: 'Browser does not support media access',
                fix: 'Use Chrome, Firefox, Safari, or Edge'
            });
        }

        if (!window.RTCPeerConnection) {
            issues.push({
                type: 'browser',
                message: 'Browser does not support WebRTC',
                fix: 'Update your browser to a newer version'
            });
        }

        // Check socket connection
        if (!window.socket || !window.socket.connected) {
            issues.push({
                type: 'connection',
                message: 'Not connected to server',
                fix: 'Refresh the page and wait for connection'
            });
        }

        // Check permissions
        const permStatus = await this.checkPermissionStatus();
        if (permStatus.permissions.microphone === 'denied') {
            issues.push({
                type: 'permission',
                message: 'Microphone permission denied',
                fix: 'Click microphone icon in address bar and allow access'
            });
        }

        // Check for available devices
        try {
            const devices = await navigator.mediaDevices.enumerateDevices();
            const audioInputs = devices.filter(d => d.kind === 'audioinput');
            const videoInputs = devices.filter(d => d.kind === 'videoinput');

            console.log(`🎤 Audio devices found: ${audioInputs.length}`);
            console.log(`📷 Video devices found: ${videoInputs.length}`);

            if (audioInputs.length === 0) {
                issues.push({
                    type: 'hardware',
                    message: 'No microphone devices found',
                    fix: 'Connect a microphone or headset with built-in mic'
                });
            }

            // For video calls, warn if no camera but don't block the call
            if (callType === 'video' && videoInputs.length === 0) {
                console.warn('⚠️ No camera found for video call, will fall back to audio-only');
                // Don't add this as a blocking issue, just log it
            }
        } catch (deviceError) {
            console.warn('Could not enumerate devices:', deviceError);
        }

        // Check for available devices
        try {
            const devices = await navigator.mediaDevices.enumerateDevices();
            const audioInputs = devices.filter(d => d.kind === 'audioinput');
            const videoInputs = devices.filter(d => d.kind === 'videoinput');

            if (audioInputs.length === 0) {
                issues.push({
                    type: 'hardware',
                    message: 'No microphone devices found',
                    fix: 'Connect a microphone or headset with built-in mic'
                });
            }

            if (callType === 'video' && videoInputs.length === 0) {
                issues.push({
                    type: 'hardware',
                    message: 'No camera devices found',
                    fix: 'Connect a webcam or use audio-only calls instead'
                });
            }
        } catch (deviceError) {
            console.warn('Could not enumerate devices:', deviceError);
        }

        console.log('🛫 Preflight check results:', {
            passed: issues.length === 0,
            issues: issues
        });

        return {
            passed: issues.length === 0,
            issues: issues,
            status: permStatus
        };
    }

    async testMediaAccess(includeVideo = false) {
        try {
            console.log('🧪 Testing media access...');

            // Check available devices first
            let actualIncludeVideo = includeVideo;
            try {
                const devices = await navigator.mediaDevices.enumerateDevices();
                const videoInputs = devices.filter(d => d.kind === 'videoinput');
                const audioInputs = devices.filter(d => d.kind === 'audioinput');

                if (includeVideo && videoInputs.length === 0) {
                    console.warn('📷 No camera found, testing audio only');
                    actualIncludeVideo = false;
                    this.showCallError('📷 No camera found, testing microphone only...', 'info');
                }

                if (audioInputs.length === 0) {
                    throw new Error('No microphone found. Please connect a microphone.');
                }
            } catch (deviceError) {
                console.warn('Could not check devices, proceeding with test');
            }

            const constraints = {
                audio: true,
                video: actualIncludeVideo
            };

            console.log('🧪 Testing with constraints:', constraints);

            const stream = await navigator.mediaDevices.getUserMedia(constraints);
            console.log('✅ Media access test successful:', stream);

            // Stop the test stream immediately
            stream.getTracks().forEach(track => track.stop());

            const message = actualIncludeVideo ?
                '✅ Microphone and camera test successful! You can make video calls.' :
                '✅ Microphone test successful! You can make audio calls.';

            this.showCallError(message, 'success');
            return true;

        } catch (error) {
            console.error('❌ Media access test failed:', error);

            let errorMessage = 'Media access test failed: ';

            if (error.name === 'NotAllowedError') {
                errorMessage += 'Permission denied. Please click the microphone icon in your browser address bar and allow access.';
            } else if (error.name === 'NotFoundError') {
                if (error.message.includes('video') || includeVideo) {
                    errorMessage += 'No camera found (this is OK for audio calls). Microphone test: ';
                    // Try audio-only test
                    try {
                        const audioStream = await navigator.mediaDevices.getUserMedia({ audio: true });
                        audioStream.getTracks().forEach(track => track.stop());
                        this.showCallError('🎤 Microphone works! Camera not found, but audio calls are available.', 'success');
                        return true;
                    } catch (audioError) {
                        errorMessage += 'No microphone found either. Please connect a microphone.';
                    }
                } else {
                    errorMessage += 'No microphone found. Please check your microphone is connected.';
                }
            } else if (error.name === 'NotSupportedError') {
                errorMessage += 'Media devices not supported in this browser. Try Chrome, Firefox, or Safari.';
            } else if (error.name === 'NotReadableError') {
                errorMessage += 'Microphone is already in use by another application. Please close other apps.';
            } else if (error.name === 'OverconstrainedError') {
                errorMessage += 'Device constraints cannot be satisfied. Try refreshing the page.';
            } else if (error.name === 'SecurityError') {
                errorMessage += 'Security error. Please use HTTPS or localhost.';
            } else {
                errorMessage += error.message;
            }

            this.showCallError(errorMessage);
            return false;
        }
    }

    async testMicrophoneOnly() {
        try {
            console.log('🎤 Testing microphone only (no camera)...');

            const constraints = { audio: true, video: false };

            this.showCallError('🎤 Testing microphone access...', 'info');

            const stream = await navigator.mediaDevices.getUserMedia(constraints);
            console.log('✅ Microphone test successful:', stream);

            // Stop the test stream immediately
            stream.getTracks().forEach(track => track.stop());

            this.showCallError('✅ Microphone works perfectly! Audio calls are ready.', 'success');
            return true;

        } catch (error) {
            console.error('❌ Microphone test failed:', error);

            let errorMessage = '❌ Microphone test failed: ';

            if (error.name === 'NotAllowedError') {
                errorMessage += 'Permission denied. Please click the 🎤 icon in your browser address bar and allow microphone access.';
            } else if (error.name === 'NotFoundError') {
                errorMessage += 'No microphone found. Please check your microphone is connected and working.';
            } else if (error.name === 'NotReadableError') {
                errorMessage += 'Microphone is being used by another app. Please close other apps and try again.';
            } else if (error.name === 'SecurityError') {
                errorMessage += 'Security error. Please use HTTPS or localhost.';
            } else {
                errorMessage += error.message;
            }

            this.showCallError(errorMessage);
            return false;
        }
    }

    async initiateAudioOnlyCall(receiverId) {
        try {
            console.log(`🎤 Initiating AUDIO-ONLY call to user ${receiverId}`);

            // Basic checks only
            if (!window.isSecureContext) {
                throw new Error('HTTPS or localhost required for microphone access');
            }

            if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
                throw new Error('Browser does not support microphone access');
            }

            // Enhanced socket connection check
            this.showCallError('🔌 Checking server connection...', 'info');

            const socketReady = await this.ensureSocketConnection();
            if (!socketReady) {
                throw new Error('Could not establish server connection. Please refresh the page and try again.');
            }

            console.log('✅ Socket connection verified');

            // Show status
            this.showCallError('🎤 Accessing microphone for audio call...', 'info');

            // Request ONLY audio - never video
            const constraints = {
                audio: {
                    echoCancellation: true,
                    noiseSuppression: true,
                    autoGainControl: true
                },
                video: false  // NEVER request video
            };

            console.log('📱 Requesting AUDIO-ONLY access:', constraints);

            // Get audio stream with timeout
            const mediaTimeout = new Promise((_, reject) => {
                setTimeout(() => reject(new Error('Microphone request timed out')), 10000);
            });

            const mediaRequest = navigator.mediaDevices.getUserMedia(constraints);
            this.localStream = await Promise.race([mediaRequest, mediaTimeout]);

            console.log('✅ Audio-only stream obtained:', this.localStream);

            // Create peer connection
            this.createPeerConnection();

            // Add audio tracks only
            this.localStream.getTracks().forEach(track => {
                console.log(`🎵 Adding ${track.kind} track:`, track.label);
                this.peerConnection.addTrack(track, this.localStream);
            });

            // Store call info
            this.currentCall = {
                receiverId: receiverId,
                callType: 'audio',
                isInitiator: true
            };

            // Emit call initiation
            console.log('📤 Emitting audio call initiation to receiver:', receiverId);
            console.log('📤 Call data being sent:', {
                receiver_id: receiverId,
                call_type: 'audio'
            });

            window.socket.emit('initiate_call', {
                receiver_id: receiverId,
                call_type: 'audio'
            });

            // Show calling interface
            this.showCallingInterface(receiverId, 'audio');

            // Clear status message
            setTimeout(() => {
                const toastContainer = document.getElementById('toastContainer');
                if (toastContainer) {
                    toastContainer.innerHTML = '';
                }
            }, 2000);

        } catch (error) {
            console.error('❌ Audio-only call failed:', error);

            let errorMessage = '❌ Audio call failed: ';

            if (error.name === 'NotAllowedError') {
                errorMessage += 'Microphone permission denied. Please click the 🎤 icon in your browser address bar and allow access.';
            } else if (error.name === 'NotFoundError') {
                errorMessage += 'No microphone found. Please check your microphone is connected.';
            } else if (error.name === 'NotReadableError') {
                errorMessage += 'Microphone is being used by another app. Please close other apps and try again.';
            } else {
                errorMessage += error.message;
            }

            this.showCallError(errorMessage);
            this.cleanup();
        }
    }

    bindSocketEvents() {
        if (!window.socket) {
            console.error('❌ Socket.IO not available for WebRTC');
            return;
        }

        console.log('🔗 Binding WebRTC socket events...');

        // Incoming call
        window.socket.on('incoming_call', (data) => {
            console.log('📞 Received incoming_call event:', data);
            this.handleIncomingCall(data);
        });

        // Call initiated confirmation
        window.socket.on('call_initiated', (data) => {
            console.log('📞 Received call_initiated event:', data);
            this.handleCallInitiated(data);
        });

        // Call accepted/answered
        window.socket.on('call_accepted', (data) => {
            console.log('✅ Received call_accepted event:', data);
            this.handleCallAccepted(data);
        });

        window.socket.on('call_answered', (data) => {
            console.log('✅ Received call_answered event:', data);
            this.handleCallAccepted(data);
        });

        // Call rejected
        window.socket.on('call_rejected', (data) => {
            console.log('❌ Received call_rejected event:', data);
            this.handleCallRejected(data);
        });

        // Call ended
        window.socket.on('call_ended', (data) => {
            console.log('📞 Received call_ended event:', data);
            this.handleCallEnded(data);
        });

        // Call error
        window.socket.on('call_error', (data) => {
            console.log('❌ Received call_error event:', data);
            this.showCallError(data.message || 'Call error occurred');
        });

        // WebRTC signaling
        window.socket.on('webrtc_offer', (data) => {
            console.log('📡 Received webrtc_offer event:', data);
            this.handleWebRTCOffer(data);
        });

        window.socket.on('webrtc_answer', (data) => {
            console.log('📡 Received webrtc_answer event:', data);
            this.handleWebRTCAnswer(data);
        });

        window.socket.on('webrtc_ice_candidate', (data) => {
            console.log('🧊 Received webrtc_ice_candidate event:', data);
            this.handleICECandidate(data);
        });

        console.log('✅ WebRTC socket events bound successfully');
    }
    
    async initiateCall(receiverId, callType = 'audio') {
        try {
            console.log(`📞 Initiating ${callType} call to user ${receiverId}`);

            // Run comprehensive preflight check
            const preflightResult = await this.preflightCheck(callType);

            if (!preflightResult.passed) {
                console.error('❌ Preflight check failed:', preflightResult.issues);

                let errorMessage = '❌ Cannot start call:\n\n';
                preflightResult.issues.forEach((issue, index) => {
                    errorMessage += `${index + 1}. ${issue.message}\n   Fix: ${issue.fix}\n\n`;
                });

                throw new Error(errorMessage);
            }

            console.log('✅ Preflight check passed');

            // Show permission request message
            this.showCallError('🎤 Accessing microphone for call...', 'info');

            // Check available devices first
            let actualCallType = callType;
            try {
                const devices = await navigator.mediaDevices.enumerateDevices();
                const videoInputs = devices.filter(d => d.kind === 'videoinput');

                if (callType === 'video' && videoInputs.length === 0) {
                    console.log('📷 No camera found, automatically starting audio call instead');
                    actualCallType = 'audio';
                    this.showCallError('📷 No camera detected - starting audio call...', 'info');
                }
            } catch (e) {
                console.warn('Could not check devices, proceeding with original call type');
            }

            // Get user media with optimized constraints
            const constraints = {
                audio: {
                    echoCancellation: true,
                    noiseSuppression: true,
                    autoGainControl: true
                },
                video: actualCallType === 'video' ? {
                    width: { ideal: 640, max: 1280 },
                    height: { ideal: 480, max: 720 },
                    frameRate: { ideal: 30, max: 60 }
                } : false
            };

            console.log('📱 Requesting media access with constraints:', constraints);
            console.log('📞 Actual call type:', actualCallType);

            // Add timeout for media request
            const mediaTimeout = new Promise((_, reject) => {
                setTimeout(() => reject(new Error('Media request timed out after 15 seconds')), 15000);
            });

            let mediaRequest;
            try {
                mediaRequest = navigator.mediaDevices.getUserMedia(constraints);
                this.localStream = await Promise.race([mediaRequest, mediaTimeout]);
            } catch (mediaError) {
                // If video fails, try audio-only as fallback
                if (actualCallType === 'video' && mediaError.name === 'NotFoundError') {
                    console.warn('📷 Video failed, trying audio-only fallback...');
                    actualCallType = 'audio';
                    const audioOnlyConstraints = { audio: constraints.audio, video: false };
                    mediaRequest = navigator.mediaDevices.getUserMedia(audioOnlyConstraints);
                    this.localStream = await Promise.race([mediaRequest, mediaTimeout]);
                    this.showCallError('📷 Camera not available, using audio-only call', 'info');
                } else {
                    throw mediaError;
                }
            }

            // Update call type if it changed
            if (actualCallType !== callType) {
                callType = actualCallType;
                if (this.currentCall) {
                    this.currentCall.callType = actualCallType;
                }
            }

            console.log('✅ Media access granted:', this.localStream);
            console.log('📊 Stream tracks:', this.localStream.getTracks().map(t => `${t.kind}: ${t.label} (${t.readyState})`));

            // Clear the permission request message
            setTimeout(() => {
                const toastContainer = document.getElementById('toastContainer');
                if (toastContainer) {
                    toastContainer.innerHTML = '';
                }
            }, 1000);

            // Create peer connection
            this.createPeerConnection();

            // Add local stream to peer connection
            this.localStream.getTracks().forEach(track => {
                console.log('🎵 Adding track:', track.kind, track.label);
                this.peerConnection.addTrack(track, this.localStream);
            });

            // Store call info with actual call type
            this.currentCall = {
                receiverId: receiverId,
                callType: actualCallType,
                isInitiator: true
            };

            // Check socket connection
            if (!window.socket || !window.socket.connected) {
                throw new Error('Socket not connected. Please refresh the page.');
            }

            // Emit call initiation with actual call type
            console.log('📤 Emitting initiate_call event:', {
                receiver_id: receiverId,
                call_type: actualCallType
            });

            window.socket.emit('initiate_call', {
                receiver_id: receiverId,
                call_type: actualCallType
            });

            // Show calling interface with actual call type
            this.showCallingInterface(receiverId, actualCallType);

        } catch (error) {
            console.error('❌ Error initiating call:', error);
            let errorMessage = 'Failed to access camera/microphone';

            if (error.name === 'NotAllowedError') {
                errorMessage = 'Permission denied. Please allow access to camera/microphone and try again.';
            } else if (error.name === 'NotFoundError') {
                errorMessage = 'No camera/microphone found. Please check your devices.';
            } else if (error.name === 'NotSupportedError') {
                errorMessage = 'Camera/microphone not supported in this browser.';
            } else if (error.name === 'NotReadableError') {
                errorMessage = 'Camera/microphone is already in use by another application.';
            } else if (error.name === 'OverconstrainedError') {
                errorMessage = 'Camera/microphone constraints cannot be satisfied.';
            } else if (error.name === 'SecurityError') {
                errorMessage = 'Security error. Please use HTTPS or localhost.';
            }

            this.showCallError(errorMessage);
        }
    }

    async ensureSocketConnection() {
        console.log('🔌 Ensuring socket connection...');

        // Check if socket exists
        if (!window.socket) {
            console.log('❌ No socket object found');
            return false;
        }

        // If already connected, return true
        if (window.socket.connected) {
            console.log('✅ Socket already connected');
            return true;
        }

        console.log('🔄 Socket not connected, attempting to connect...');

        // Try to connect
        try {
            window.socket.connect();
        } catch (error) {
            console.error('❌ Error during socket connect:', error);
            return false;
        }

        // Wait for connection with timeout (10 seconds)
        const maxAttempts = 100; // 10 seconds at 100ms intervals
        let attempts = 0;

        while (!window.socket.connected && attempts < maxAttempts) {
            await new Promise(resolve => setTimeout(resolve, 100));
            attempts++;

            if (attempts % 10 === 0) {
                console.log(`🔄 Still waiting for connection... (${attempts/10}s)`);
            }
        }

        if (window.socket.connected) {
            console.log('✅ Socket connection established');
            return true;
        } else {
            console.error('❌ Socket connection timeout');
            return false;
        }
    }

    createPeerConnection() {
        this.peerConnection = new RTCPeerConnection(this.pcConfig);
        
        // Handle ICE candidates
        this.peerConnection.onicecandidate = (event) => {
            if (event.candidate && this.currentCall) {
                window.socket.emit('webrtc_ice_candidate', {
                    call_id: this.currentCall.callId,
                    target_user_id: this.currentCall.receiverId,
                    candidate: event.candidate
                });
            }
        };
        
        // Handle remote stream
        this.peerConnection.ontrack = (event) => {
            console.log('📡 Received remote stream');
            console.log('📊 Remote stream tracks:', event.streams[0].getTracks().map(t => `${t.kind}: ${t.label}`));
            this.remoteStream = event.streams[0];

            // Handle both video and audio elements
            const remoteVideo = document.getElementById('remoteVideo');
            const remoteAudio = document.getElementById('remoteAudio');

            if (remoteVideo) {
                remoteVideo.srcObject = this.remoteStream;
                console.log('✅ Remote stream connected to video element');
            }

            if (remoteAudio) {
                remoteAudio.srcObject = this.remoteStream;
                console.log('✅ Remote stream connected to audio element');
            }

            // For audio-only calls, ensure audio plays
            if (this.currentCall && this.currentCall.callType === 'audio') {
                const audioTracks = this.remoteStream.getAudioTracks();
                if (audioTracks.length > 0) {
                    console.log('🔊 Audio-only call - ensuring audio playback');
                    // Create audio element if needed
                    if (!remoteAudio) {
                        const audioElement = document.createElement('audio');
                        audioElement.id = 'remoteAudio';
                        audioElement.autoplay = true;
                        audioElement.srcObject = this.remoteStream;
                        document.body.appendChild(audioElement);
                        console.log('✅ Created audio element for remote stream');
                    }
                }
            }
        };
        
        // Handle connection state changes
        this.peerConnection.onconnectionstatechange = () => {
            console.log('🔗 Connection state:', this.peerConnection.connectionState);
            if (this.peerConnection.connectionState === 'connected') {
                this.onCallConnected();
            } else if (this.peerConnection.connectionState === 'disconnected' || 
                       this.peerConnection.connectionState === 'failed') {
                this.endCall();
            }
        };
    }
    
    async handleIncomingCall(data) {
        console.log('📞 Incoming call received:', data);
        console.log('📞 Call details:', {
            call_id: data.call_id,
            caller_id: data.caller_id,
            caller_username: data.caller_username,
            call_type: data.call_type
        });

        this.currentCall = {
            callId: data.call_id,
            callerId: data.caller_id,
            callerName: data.caller_username,
            callerAvatar: data.caller_avatar_color,
            callerProfilePicture: data.caller_profile_picture,
            callType: data.call_type,
            isInitiator: false
        };

        console.log('📞 Current call object set:', this.currentCall);

        this.showIncomingCallModal(data);
        console.log('📞 Incoming call modal should be displayed');
    }

    handleCallInitiated(data) {
        console.log('📞 Call initiated successfully:', data);
        console.log('📞 Current call object before update:', this.currentCall);

        if (this.currentCall) {
            this.currentCall.callId = data.call_id;
            console.log('✅ Call ID set:', data.call_id);
            console.log('📞 Current call object after update:', this.currentCall);
        } else {
            console.error('❌ No current call object when handling call initiated');
        }

        // Update UI to show "calling" status
        const status = document.getElementById('activeCallStatus');
        if (status) {
            status.textContent = 'Calling...';
            console.log('✅ Updated call status to "Calling..."');
        } else {
            console.warn('⚠️ activeCallStatus element not found');
        }
    }
    
    showIncomingCallModal(data) {
        console.log('📱 Showing incoming call modal for:', data.caller_username);

        const modal = document.getElementById('incomingCallModal');
        const callerName = document.getElementById('callerName');
        const callerAvatar = document.getElementById('callerAvatar');
        const callType = document.getElementById('incomingCallType');

        console.log('📱 Modal elements found:', {
            modal: !!modal,
            callerName: !!callerName,
            callerAvatar: !!callerAvatar,
            callType: !!callType
        });

        if (callerName) {
            callerName.textContent = data.caller_username;
            console.log('✅ Set caller name:', data.caller_username);
        }

        if (callType) {
            const typeText = data.call_type === 'video' ? 'Video Call' : 'Audio Call';
            callType.textContent = typeText;
            console.log('✅ Set call type:', typeText);
        }

        // Set caller avatar
        if (callerAvatar) {
            if (data.caller_profile_picture) {
                callerAvatar.innerHTML = `<img src="${data.caller_profile_picture}" alt="${data.caller_username}">`;
                console.log('✅ Set caller profile picture');
            } else {
                callerAvatar.style.background = data.caller_avatar_color;
                callerAvatar.innerHTML = '<i class="fas fa-user"></i>';
                console.log('✅ Set caller avatar color:', data.caller_avatar_color);
            }
        }

        if (modal) {
            modal.style.display = 'flex';
            console.log('✅ Incoming call modal displayed');

            // Also add a toast notification as backup
            if (window.showToast) {
                window.showToast(`📞 Incoming ${data.call_type} call from ${data.caller_username}`, 'info');
            }
        } else {
            console.error('❌ Incoming call modal not found!');
        }

        // Play ringtone (optional)
        this.playRingtone();
    }
    
    async acceptCall() {
        try {
            console.log('✅ Accepting call');

            if (!this.currentCall) return;

            // Check if getUserMedia is available
            if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
                throw new Error('getUserMedia is not supported in this browser');
            }

            // Get user media with detailed constraints
            const constraints = {
                audio: {
                    echoCancellation: true,
                    noiseSuppression: true,
                    autoGainControl: true
                },
                video: this.currentCall.callType === 'video' ? {
                    width: { ideal: 640 },
                    height: { ideal: 480 },
                    frameRate: { ideal: 30 }
                } : false
            };

            console.log('📱 Requesting media access for call acceptance:', constraints);

            this.localStream = await navigator.mediaDevices.getUserMedia(constraints);
            console.log('✅ Media access granted for call acceptance:', this.localStream);

            // Create peer connection
            this.createPeerConnection();

            // Add local stream
            this.localStream.getTracks().forEach(track => {
                console.log('🎵 Adding track for call acceptance:', track.kind, track.label);
                this.peerConnection.addTrack(track, this.localStream);
            });

            // Hide incoming call modal
            const incomingModal = document.getElementById('incomingCallModal');
            if (incomingModal) incomingModal.style.display = 'none';

            // Emit call acceptance
            window.socket.emit('answer_call', {
                call_id: this.currentCall.callId,
                caller_id: this.currentCall.callerId
            });

            // Show active call interface
            this.showActiveCallInterface();

        } catch (error) {
            console.error('❌ Error accepting call:', error);

            let errorMessage = 'Failed to access camera/microphone';

            if (error.name === 'NotAllowedError') {
                errorMessage = 'Permission denied. Please allow access to camera/microphone and try again.';
            } else if (error.name === 'NotFoundError') {
                errorMessage = 'No camera/microphone found. Please check your devices.';
            } else if (error.name === 'NotSupportedError') {
                errorMessage = 'Camera/microphone not supported in this browser.';
            } else if (error.name === 'NotReadableError') {
                errorMessage = 'Camera/microphone is already in use by another application.';
            } else if (error.name === 'OverconstrainedError') {
                errorMessage = 'Camera/microphone constraints cannot be satisfied.';
            } else if (error.name === 'SecurityError') {
                errorMessage = 'Security error. Please use HTTPS or localhost.';
            }

            this.showCallError(errorMessage);
            this.rejectCall();
        }
    }
    
    rejectCall() {
        console.log('❌ Rejecting call');
        
        if (this.currentCall && this.currentCall.callId) {
            window.socket.emit('reject_call', {
                call_id: this.currentCall.callId
            });
        }
        
        this.hideIncomingCallModal();
        this.cleanup();
    }
    
    endCall() {
        console.log('📞 Ending call');
        
        if (this.currentCall && this.currentCall.callId) {
            window.socket.emit('end_call', {
                call_id: this.currentCall.callId
            });
        }
        
        this.cleanup();
        this.hideAllCallModals();
    }
    
    cleanup() {
        // Stop local stream
        if (this.localStream) {
            this.localStream.getTracks().forEach(track => track.stop());
            this.localStream = null;
        }
        
        // Close peer connection
        if (this.peerConnection) {
            this.peerConnection.close();
            this.peerConnection = null;
        }
        
        // Clear call timer
        if (this.callTimer) {
            clearInterval(this.callTimer);
            this.callTimer = null;
        }
        
        // Reset state
        this.currentCall = null;
        this.isCallActive = false;
        this.isMinimized = false;
        this.callStartTime = null;
        this.isMuted = false;
        this.isCameraOff = false;
        
        this.stopRingtone();
    }
    
    hideAllCallModals() {
        const modals = ['incomingCallModal', 'activeCallModal', 'callMinimized'];
        modals.forEach(modalId => {
            const modal = document.getElementById(modalId);
            if (modal) modal.style.display = 'none';
        });
    }
    
    hideIncomingCallModal() {
        const modal = document.getElementById('incomingCallModal');
        if (modal) modal.style.display = 'none';
        this.stopRingtone();
    }
    
    playRingtone() {
        // Create audio element for ringtone (optional)
        // You can add an actual ringtone file here
        console.log('🔔 Playing ringtone...');
    }
    
    stopRingtone() {
        console.log('🔕 Stopping ringtone...');
    }
    
    showCallError(message, type = 'error') {
        if (window.showToast) {
            window.showToast(message, type);
        } else {
            alert(message);
        }
    }

    async testAudioStreaming() {
        """Comprehensive audio streaming test."""
        console.log('🧪 Starting comprehensive audio streaming test...');

        try {
            // Test 1: Basic connectivity
            console.log('🔌 Testing server connectivity...');
            const connectivityResult = await this.testServerConnectivity();

            // Test 2: Media permissions
            console.log('🎤 Testing media permissions...');
            const permissionResult = await this.testMediaPermissions();

            // Test 3: Audio quality
            console.log('📊 Testing audio quality...');
            const qualityResult = await this.testAudioQuality();

            // Test 4: WebRTC connectivity
            console.log('🌐 Testing WebRTC connectivity...');
            const webrtcResult = await this.testWebRTCConnectivity();

            const overallResult = {
                connectivity: connectivityResult,
                permissions: permissionResult,
                quality: qualityResult,
                webrtc: webrtcResult,
                overall_score: this.calculateOverallScore([connectivityResult, permissionResult, qualityResult, webrtcResult])
            };

            console.log('✅ Audio streaming test completed:', overallResult);
            this.displayTestResults(overallResult);

            return overallResult;

        } catch (error) {
            console.error('❌ Audio streaming test failed:', error);
            this.showCallError('Audio streaming test failed: ' + error.message);
            return { error: error.message };
        }
    }

    async testServerConnectivity() {
        """Test Socket.IO server connectivity."""
        return new Promise((resolve) => {
            const startTime = Date.now();

            if (!window.socket || !window.socket.connected) {
                resolve({ passed: false, message: 'Socket not connected', latency: null });
                return;
            }

            const testId = 'test_' + Date.now();

            const timeout = setTimeout(() => {
                resolve({ passed: false, message: 'Server response timeout', latency: null });
            }, 5000);

            window.socket.once('audio_stream_test_response', (data) => {
                clearTimeout(timeout);
                const latency = Date.now() - startTime;
                resolve({
                    passed: true,
                    message: 'Server connectivity OK',
                    latency: latency,
                    server_time: data.server_time
                });
            });

            window.socket.emit('test_audio_stream', {
                test_type: 'basic',
                test_id: testId,
                client_time: new Date().toISOString()
            });
        });
    }

    async testMediaPermissions() {
        """Test media permissions and device access."""
        try {
            const stream = await navigator.mediaDevices.getUserMedia({
                audio: {
                    echoCancellation: true,
                    noiseSuppression: true,
                    autoGainControl: true
                }
            });

            const audioTracks = stream.getAudioTracks();
            const deviceInfo = {
                device_count: audioTracks.length,
                devices: audioTracks.map(track => ({
                    label: track.label,
                    kind: track.kind,
                    enabled: track.enabled,
                    muted: track.muted,
                    ready_state: track.readyState
                }))
            };

            // Stop the test stream
            stream.getTracks().forEach(track => track.stop());

            return {
                passed: true,
                message: 'Media permissions granted',
                device_info: deviceInfo
            };

        } catch (error) {
            return {
                passed: false,
                message: 'Media permission denied: ' + error.message,
                error_name: error.name
            };
        }
    }

    async testAudioQuality() {
        """Test audio quality and processing."""
        return new Promise((resolve) => {
            const testId = 'quality_' + Date.now();

            const timeout = setTimeout(() => {
                resolve({ passed: false, message: 'Audio quality test timeout' });
            }, 10000);

            window.socket.once('audio_quality_response', (data) => {
                clearTimeout(timeout);
                resolve({
                    passed: data.quality_score > 70,
                    message: `Audio quality: ${data.quality_score}%`,
                    quality_score: data.quality_score,
                    latency: data.latency
                });
            });

            window.socket.emit('test_audio_stream', {
                test_type: 'quality',
                test_id: testId,
                latency: Date.now()
            });
        });
    }

    async testWebRTCConnectivity() {
        """Test WebRTC peer connection capabilities."""
        try {
            const pc = new RTCPeerConnection(this.pcConfig);

            // Test ICE gathering
            const iceGatheringPromise = new Promise((resolve) => {
                const candidates = [];
                const timeout = setTimeout(() => {
                    resolve({ ice_candidates: candidates.length, timeout: true });
                }, 5000);

                pc.onicecandidate = (event) => {
                    if (event.candidate) {
                        candidates.push(event.candidate.type);
                    } else {
                        clearTimeout(timeout);
                        resolve({ ice_candidates: candidates.length, timeout: false });
                    }
                };

                // Create a dummy offer to start ICE gathering
                pc.createOffer().then(offer => pc.setLocalDescription(offer));
            });

            const iceResult = await iceGatheringPromise;
            pc.close();

            return {
                passed: iceResult.ice_candidates > 0,
                message: `WebRTC connectivity: ${iceResult.ice_candidates} ICE candidates`,
                ice_candidates: iceResult.ice_candidates,
                ice_timeout: iceResult.timeout
            };

        } catch (error) {
            return {
                passed: false,
                message: 'WebRTC test failed: ' + error.message,
                error: error.name
            };
        }
    }

    calculateOverallScore(results) {
        """Calculate overall test score."""
        const passedTests = results.filter(r => r.passed).length;
        return Math.round((passedTests / results.length) * 100);
    }

    displayTestResults(results) {
        """Display test results to user."""
        const score = results.overall_score;
        let message = `🧪 Audio Streaming Test Results: ${score}%\n\n`;

        message += `🔌 Server Connectivity: ${results.connectivity.passed ? '✅' : '❌'} ${results.connectivity.message}\n`;
        message += `🎤 Media Permissions: ${results.permissions.passed ? '✅' : '❌'} ${results.permissions.message}\n`;
        message += `📊 Audio Quality: ${results.quality.passed ? '✅' : '❌'} ${results.quality.message}\n`;
        message += `🌐 WebRTC Connectivity: ${results.webrtc.passed ? '✅' : '❌'} ${results.webrtc.message}\n`;

        if (score >= 75) {
            message += '\n🎉 Audio streaming should work well!';
            this.showCallError(message, 'success');
        } else if (score >= 50) {
            message += '\n⚠️ Audio streaming may have issues. Check failed tests above.';
            this.showCallError(message, 'warning');
        } else {
            message += '\n❌ Audio streaming likely won\'t work. Please fix the issues above.';
            this.showCallError(message, 'error');
        }
    }

    async handleCallAccepted(data) {
        console.log('✅ Call accepted:', data);

        if (!this.currentCall) return;

        this.currentCall.callId = data.call_id;
        this.currentCall.receiverName = data.receiver_username;

        // Create WebRTC offer
        try {
            const offer = await this.peerConnection.createOffer();
            await this.peerConnection.setLocalDescription(offer);

            // Send offer to receiver
            window.socket.emit('webrtc_offer', {
                call_id: data.call_id,
                target_user_id: data.receiver_id,
                receiver_id: data.receiver_id,
                offer: offer
            });

            this.showActiveCallInterface();

        } catch (error) {
            console.error('❌ Error creating offer:', error);
            this.endCall();
        }
    }

    handleCallRejected(data) {
        console.log('❌ Call rejected:', data);
        this.showCallError('Call was declined');
        this.cleanup();
        this.hideAllCallModals();
    }

    handleCallEnded(data) {
        console.log('📞 Call ended by other party:', data);
        this.cleanup();
        this.hideAllCallModals();
        if (window.showToast) {
            window.showToast('Call ended', 'info');
        }
    }

    async handleWebRTCOffer(data) {
        console.log('📡 Received WebRTC offer:', data);

        if (!this.peerConnection) return;

        try {
            await this.peerConnection.setRemoteDescription(data.offer);

            // Create answer
            const answer = await this.peerConnection.createAnswer();
            await this.peerConnection.setLocalDescription(answer);

            // Send answer back
            window.socket.emit('webrtc_answer', {
                call_id: data.call_id,
                target_user_id: data.from_user_id,
                caller_id: data.from_user_id,
                answer: answer
            });

        } catch (error) {
            console.error('❌ Error handling WebRTC offer:', error);
            this.endCall();
        }
    }

    async handleWebRTCAnswer(data) {
        console.log('📡 Received WebRTC answer:', data);

        if (!this.peerConnection) return;

        try {
            await this.peerConnection.setRemoteDescription(data.answer);
        } catch (error) {
            console.error('❌ Error handling WebRTC answer:', error);
            this.endCall();
        }
    }

    async handleICECandidate(data) {
        console.log('🧊 Received ICE candidate:', data);

        if (!this.peerConnection) return;

        try {
            await this.peerConnection.addIceCandidate(data.candidate);
        } catch (error) {
            console.error('❌ Error adding ICE candidate:', error);
        }
    }

    showCallingInterface(receiverId, callType) {
        console.log('📞 Showing calling interface for receiver:', receiverId);

        // Show active call modal in "calling" state
        const modal = document.getElementById('activeCallModal');
        const status = document.getElementById('activeCallStatus');
        const name = document.getElementById('activeCallName');
        const avatar = document.getElementById('activeCallAvatar');
        const videoContainer = document.getElementById('videoContainer');
        const audioIndicator = document.getElementById('audioIndicator');
        const cameraBtn = document.getElementById('cameraBtn');

        if (status) {
            status.textContent = 'Calling...';
            console.log('✅ Set call status to "Calling..."');
        }

        // Get contact name from global variables
        const contactName = window.currentContactName || 'Contact';
        const contactColor = window.currentContactColor || '#ff6b9d';
        const contactProfilePicture = window.currentContactProfilePicture;

        console.log('📞 Contact info for calling interface:', {
            name: contactName,
            color: contactColor,
            profilePicture: contactProfilePicture
        });

        if (name) {
            name.textContent = contactName;
            console.log('✅ Set contact name to:', contactName);
        }

        // Set contact avatar
        if (avatar) {
            if (contactProfilePicture) {
                avatar.innerHTML = `<img src="${contactProfilePicture}" alt="${contactName}">`;
                console.log('✅ Set contact profile picture');
            } else {
                avatar.style.background = contactColor;
                avatar.innerHTML = '<i class="fas fa-user"></i>';
                console.log('✅ Set contact avatar color:', contactColor);
            }
        }

        // Show/hide video elements based on call type
        if (videoContainer && audioIndicator) {
            if (callType === 'video') {
                videoContainer.style.display = 'block';
                audioIndicator.style.display = 'none';
                if (cameraBtn) cameraBtn.style.display = 'block';

                // Show local video
                const localVideo = document.getElementById('localVideo');
                if (localVideo && this.localStream) {
                    localVideo.srcObject = this.localStream;
                }
            } else {
                videoContainer.style.display = 'none';
                audioIndicator.style.display = 'block';
                if (cameraBtn) cameraBtn.style.display = 'none';
            }
        }

        if (modal) modal.style.display = 'flex';
    }

    showActiveCallInterface() {
        console.log('📞 Showing active call interface');

        const modal = document.getElementById('activeCallModal');
        const status = document.getElementById('activeCallStatus');
        const name = document.getElementById('activeCallName');
        const avatar = document.getElementById('activeCallAvatar');

        if (status) {
            status.textContent = 'Connected';
            console.log('✅ Set call status to "Connected"');
        }

        // Set contact name and avatar
        if (this.currentCall) {
            let contactName, contactColor, contactProfilePicture;

            if (this.currentCall.isInitiator) {
                // For outgoing calls, use global contact info
                contactName = window.currentContactName || 'Contact';
                contactColor = window.currentContactColor || '#ff6b9d';
                contactProfilePicture = window.currentContactProfilePicture;
            } else {
                // For incoming calls, use caller info
                contactName = this.currentCall.callerName;
                contactColor = this.currentCall.callerAvatar;
                contactProfilePicture = this.currentCall.callerProfilePicture;
            }

            console.log('📞 Active call contact info:', {
                name: contactName,
                color: contactColor,
                profilePicture: contactProfilePicture,
                isInitiator: this.currentCall.isInitiator
            });

            if (name) {
                name.textContent = contactName;
                console.log('✅ Set active call name to:', contactName);
            }

            if (avatar) {
                if (contactProfilePicture) {
                    avatar.innerHTML = `<img src="${contactProfilePicture}" alt="${contactName}">`;
                    console.log('✅ Set active call profile picture');
                } else {
                    avatar.style.background = contactColor;
                    avatar.innerHTML = '<i class="fas fa-user"></i>';
                    console.log('✅ Set active call avatar color:', contactColor);
                }
            }
        }

        if (modal) modal.style.display = 'flex';

        this.isCallActive = true;
        this.startCallTimer();
    }

    onCallConnected() {
        console.log('🔗 Call connected successfully');
        const status = document.getElementById('activeCallStatus');
        if (status) status.textContent = 'Connected';

        if (!this.callStartTime) {
            this.startCallTimer();
        }
    }

    startCallTimer() {
        this.callStartTime = Date.now();
        this.callTimer = setInterval(() => {
            this.updateCallDuration();
        }, 1000);
    }

    updateCallDuration() {
        if (!this.callStartTime) return;

        const elapsed = Math.floor((Date.now() - this.callStartTime) / 1000);
        const minutes = Math.floor(elapsed / 60);
        const seconds = elapsed % 60;
        const timeString = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;

        const durationElements = ['callDuration', 'minimizedDuration'];
        durationElements.forEach(id => {
            const element = document.getElementById(id);
            if (element) element.textContent = timeString;
        });
    }

    toggleMute() {
        if (!this.localStream) return;

        const audioTrack = this.localStream.getAudioTracks()[0];
        if (audioTrack) {
            this.isMuted = !this.isMuted;
            audioTrack.enabled = !this.isMuted;

            // Update UI
            const muteButtons = ['muteBtn', 'minimizedMuteBtn'];
            muteButtons.forEach(id => {
                const btn = document.getElementById(id);
                if (btn) {
                    btn.classList.toggle('muted', this.isMuted);
                    const icon = btn.querySelector('i');
                    if (icon) {
                        icon.className = this.isMuted ? 'fas fa-microphone-slash' : 'fas fa-microphone';
                    }
                }
            });
        }
    }

    toggleCamera() {
        if (!this.localStream || this.currentCall.callType !== 'video') return;

        const videoTrack = this.localStream.getVideoTracks()[0];
        if (videoTrack) {
            this.isCameraOff = !this.isCameraOff;
            videoTrack.enabled = !this.isCameraOff;

            // Update UI
            const cameraBtn = document.getElementById('cameraBtn');
            if (cameraBtn) {
                cameraBtn.classList.toggle('muted', this.isCameraOff);
                const icon = cameraBtn.querySelector('i');
                if (icon) {
                    icon.className = this.isCameraOff ? 'fas fa-video-slash' : 'fas fa-video';
                }
            }
        }
    }

    minimizeCall() {
        if (!this.isCallActive) return;

        const activeModal = document.getElementById('activeCallModal');
        const minimizedBar = document.getElementById('callMinimized');

        if (activeModal) activeModal.style.display = 'none';
        if (minimizedBar) minimizedBar.style.display = 'flex';

        this.isMinimized = true;

        // Update minimized bar info
        this.updateMinimizedCallInfo();
    }

    expandCall() {
        if (!this.isCallActive) return;

        const activeModal = document.getElementById('activeCallModal');
        const minimizedBar = document.getElementById('callMinimized');

        if (activeModal) activeModal.style.display = 'flex';
        if (minimizedBar) minimizedBar.style.display = 'none';

        this.isMinimized = false;
    }

    updateMinimizedCallInfo() {
        const name = document.getElementById('minimizedName');
        const avatar = document.getElementById('minimizedAvatar');

        if (this.currentCall && name) {
            let contactName;

            if (this.currentCall.isInitiator) {
                // For outgoing calls, use global contact info
                contactName = window.currentContactName || 'Contact';
            } else {
                // For incoming calls, use caller info
                contactName = this.currentCall.callerName;
            }

            name.textContent = contactName;
            console.log('✅ Set minimized call name to:', contactName);
        }

        if (this.currentCall && avatar) {
            let contactProfilePicture, contactColor;

            if (this.currentCall.isInitiator) {
                // For outgoing calls, use global contact info
                contactProfilePicture = window.currentContactProfilePicture;
                contactColor = window.currentContactColor || '#ff6b9d';
            } else {
                // For incoming calls, use caller info
                contactProfilePicture = this.currentCall.callerProfilePicture;
                contactColor = this.currentCall.callerAvatar;
            }

            if (contactProfilePicture) {
                avatar.innerHTML = `<img src="${contactProfilePicture}" alt="Contact">`;
                console.log('✅ Set minimized call profile picture');
            } else {
                avatar.style.background = contactColor;
                avatar.innerHTML = '<i class="fas fa-user"></i>';
                console.log('✅ Set minimized call avatar color:', contactColor);
            }
        }
    }
}

// Global call functions for HTML onclick handlers
function acceptCall() {
    if (window.webrtcManager) {
        window.webrtcManager.acceptCall();
    }
}

function rejectCall() {
    if (window.webrtcManager) {
        window.webrtcManager.rejectCall();
    }
}

function endCall() {
    if (window.webrtcManager) {
        window.webrtcManager.endCall();
    }
}

function toggleMute() {
    if (window.webrtcManager) {
        window.webrtcManager.toggleMute();
    }
}

function toggleCamera() {
    if (window.webrtcManager) {
        window.webrtcManager.toggleCamera();
    }
}

function toggleSpeaker() {
    // Speaker toggle functionality (if needed)
    console.log('🔊 Speaker toggle (not implemented)');
}

function minimizeCall() {
    if (window.webrtcManager) {
        window.webrtcManager.minimizeCall();
    }
}

function expandCall() {
    if (window.webrtcManager) {
        window.webrtcManager.expandCall();
    }
}

// Test media access function
function testMediaAccess(includeVideo = false) {
    if (window.webrtcManager) {
        return window.webrtcManager.testMediaAccess(includeVideo);
    } else {
        console.error('❌ WebRTC manager not available');
        return false;
    }
}

// Test microphone only (never tries camera)
function testMicrophoneOnly() {
    if (window.webrtcManager) {
        return window.webrtcManager.testMicrophoneOnly();
    } else {
        console.error('❌ WebRTC manager not available');
        return false;
    }
}

// Start audio-only call (never tries camera)
function startAudioOnlyCall(receiverId) {
    if (window.webrtcManager) {
        return window.webrtcManager.initiateAudioOnlyCall(receiverId);
    } else {
        console.error('❌ WebRTC manager not available');
        return false;
    }
}

// Request media permissions function
function requestMediaPermissions(includeVideo = false) {
    if (window.webrtcManager) {
        return window.webrtcManager.requestMediaPermissions(includeVideo);
    } else {
        console.error('❌ WebRTC manager not available');
        return false;
    }
}

// Run preflight check function
function runPreflightCheck(callType = 'audio') {
    if (window.webrtcManager) {
        return window.webrtcManager.preflightCheck(callType);
    } else {
        console.error('❌ WebRTC manager not available');
        return Promise.resolve({ passed: false, issues: [{ type: 'system', message: 'WebRTC manager not available' }] });
    }
}

// Test socket connection function
function testSocketConnection() {
    console.log('🧪 Testing socket connection...');
    console.log('Socket available:', !!window.socket);
    console.log('Socket connected:', window.socket ? window.socket.connected : 'N/A');
    console.log('Socket ID:', window.socket ? window.socket.id : 'N/A');

    if (window.socket && window.socket.connected) {
        console.log('✅ Socket is connected, testing echo...');
        window.socket.emit('test_echo', { message: 'Hello from client', timestamp: Date.now() });

        // Listen for echo response
        window.socket.once('test_echo_response', (data) => {
            console.log('✅ Socket echo test successful:', data);
        });

        return true;
    } else {
        console.error('❌ Socket not connected');
        return false;
    }
}

// Debug function to test call initiation
function debugCallInitiation(receiverId = 1, callType = 'audio') {
    console.log('🧪 Debug call initiation test');
    console.log('Receiver ID:', receiverId);
    console.log('Call Type:', callType);
    console.log('WebRTC Manager:', window.webrtcManager);
    console.log('Socket:', window.socket);
    console.log('Socket Connected:', window.socket ? window.socket.connected : 'N/A');

    if (!window.webrtcManager) {
        console.error('❌ WebRTC manager not available');
        return false;
    }

    if (!window.socket || !window.socket.connected) {
        console.error('❌ Socket not connected');
        return false;
    }

    try {
        console.log('🚀 Attempting to initiate call...');
        window.webrtcManager.initiateCall(receiverId, callType);
        return true;
    } catch (error) {
        console.error('❌ Error in debug call initiation:', error);
        return false;
    }
}

// Comprehensive diagnostic function
async function runFullDiagnostics() {
    console.log('🔍 Running Full Diagnostics...');
    console.log('='.repeat(60));

    const results = {
        browser: {},
        permissions: {},
        connection: {},
        media: {},
        overall: 'unknown'
    };

    // Browser checks
    results.browser.userAgent = navigator.userAgent;
    results.browser.protocol = location.protocol;
    results.browser.host = location.host;
    results.browser.secure = window.isSecureContext;
    results.browser.getUserMediaSupported = !!(navigator.mediaDevices && navigator.mediaDevices.getUserMedia);
    results.browser.webrtcSupported = !!window.RTCPeerConnection;

    console.log('🌐 Browser Info:');
    console.log('  Protocol:', results.browser.protocol);
    console.log('  Host:', results.browser.host);
    console.log('  Secure Context:', results.browser.secure);
    console.log('  getUserMedia Support:', results.browser.getUserMediaSupported);
    console.log('  WebRTC Support:', results.browser.webrtcSupported);

    // Connection checks
    results.connection.socketAvailable = !!window.socket;
    results.connection.socketConnected = window.socket ? window.socket.connected : false;
    results.connection.socketId = window.socket ? window.socket.id : null;
    results.connection.webrtcManagerAvailable = !!window.webrtcManager;

    console.log('🔗 Connection Info:');
    console.log('  Socket Available:', results.connection.socketAvailable);
    console.log('  Socket Connected:', results.connection.socketConnected);
    console.log('  Socket ID:', results.connection.socketId);
    console.log('  WebRTC Manager:', results.connection.webrtcManagerAvailable);

    // Permission checks
    if (navigator.permissions) {
        try {
            const micPerm = await navigator.permissions.query({ name: 'microphone' });
            results.permissions.microphone = micPerm.state;

            try {
                const camPerm = await navigator.permissions.query({ name: 'camera' });
                results.permissions.camera = camPerm.state;
            } catch (e) {
                results.permissions.camera = 'unknown';
            }
        } catch (e) {
            results.permissions.microphone = 'unknown';
            results.permissions.camera = 'unknown';
        }
    } else {
        results.permissions.microphone = 'api_not_available';
        results.permissions.camera = 'api_not_available';
    }

    console.log('🔐 Permission Status:');
    console.log('  Microphone:', results.permissions.microphone);
    console.log('  Camera:', results.permissions.camera);

    // Media device checks
    try {
        const devices = await navigator.mediaDevices.enumerateDevices();
        results.media.audioInputs = devices.filter(d => d.kind === 'audioinput').length;
        results.media.videoInputs = devices.filter(d => d.kind === 'videoinput').length;
        results.media.audioOutputs = devices.filter(d => d.kind === 'audiooutput').length;
    } catch (e) {
        results.media.error = e.message;
    }

    console.log('🎤 Media Devices:');
    console.log('  Audio Inputs:', results.media.audioInputs || 'unknown');
    console.log('  Video Inputs:', results.media.videoInputs || 'unknown');
    console.log('  Audio Outputs:', results.media.audioOutputs || 'unknown');

    // Overall assessment
    const issues = [];

    if (!results.browser.secure) {
        issues.push('❌ Not using HTTPS or localhost');
    }
    if (!results.browser.getUserMediaSupported) {
        issues.push('❌ Browser does not support getUserMedia');
    }
    if (!results.browser.webrtcSupported) {
        issues.push('❌ Browser does not support WebRTC');
    }
    if (!results.connection.socketConnected) {
        issues.push('❌ Not connected to server');
    }
    if (!results.connection.webrtcManagerAvailable) {
        issues.push('❌ WebRTC manager not available');
    }
    if (results.permissions.microphone === 'denied') {
        issues.push('❌ Microphone permission denied');
    }
    if (results.media.audioInputs === 0) {
        issues.push('❌ No microphone devices found');
    }

    if (issues.length === 0) {
        results.overall = 'ready';
        console.log('✅ OVERALL STATUS: READY FOR CALLS');
    } else {
        results.overall = 'issues_found';
        console.log('❌ OVERALL STATUS: ISSUES FOUND');
        console.log('🔧 Issues to fix:');
        issues.forEach((issue, index) => {
            console.log(`  ${index + 1}. ${issue}`);
        });
    }

    console.log('='.repeat(60));

    // Show user-friendly message
    if (window.webrtcManager) {
        if (results.overall === 'ready') {
            window.webrtcManager.showCallError('✅ System ready for calls! All checks passed.', 'success');
        } else {
            const issueText = issues.join('\n');
            window.webrtcManager.showCallError('❌ Issues found:\n\n' + issueText + '\n\nCheck browser console for details.');
        }
    }

    return results;
}

// Check available media devices
async function checkMediaDevices() {
    console.log('🔍 Checking available media devices...');

    try {
        const devices = await navigator.mediaDevices.enumerateDevices();

        const audioInputs = devices.filter(d => d.kind === 'audioinput');
        const videoInputs = devices.filter(d => d.kind === 'videoinput');
        const audioOutputs = devices.filter(d => d.kind === 'audiooutput');

        console.log('📱 Device Summary:');
        console.log(`🎤 Microphones: ${audioInputs.length}`);
        console.log(`📷 Cameras: ${videoInputs.length}`);
        console.log(`🔊 Speakers: ${audioOutputs.length}`);

        console.log('\n📋 Detailed Device List:');
        devices.forEach((device, index) => {
            const icon = device.kind === 'audioinput' ? '🎤' :
                        device.kind === 'videoinput' ? '📷' : '🔊';
            console.log(`${index + 1}. ${icon} ${device.kind}: ${device.label || 'Unknown device'}`);
        });

        // Show user-friendly message
        let message = `Found: ${audioInputs.length} microphone(s), ${videoInputs.length} camera(s)`;

        if (audioInputs.length > 0 && videoInputs.length > 0) {
            message += '\n✅ Both audio and video calls available!';
            if (window.webrtcManager) {
                window.webrtcManager.showCallError(message, 'success');
            }
        } else if (audioInputs.length > 0 && videoInputs.length === 0) {
            message += '\n🎤 Audio calls available, video calls will use audio-only';
            if (window.webrtcManager) {
                window.webrtcManager.showCallError(message, 'info');
            }
        } else if (audioInputs.length === 0) {
            message += '\n❌ No microphone found - calls not available';
            if (window.webrtcManager) {
                window.webrtcManager.showCallError(message, 'error');
            }
        }

        return {
            audioInputs: audioInputs.length,
            videoInputs: videoInputs.length,
            audioOutputs: audioOutputs.length,
            devices: devices
        };

    } catch (error) {
        console.error('❌ Error checking devices:', error);
        if (window.webrtcManager) {
            window.webrtcManager.showCallError('❌ Could not check devices: ' + error.message);
        }
        return null;
    }
}

// Test the video-to-audio fallback behavior
async function testVideoFallback() {
    console.log('🧪 Testing video call fallback behavior...');

    try {
        const devices = await navigator.mediaDevices.enumerateDevices();
        const cameras = devices.filter(d => d.kind === 'videoinput');
        const microphones = devices.filter(d => d.kind === 'audioinput');

        console.log(`📷 Cameras found: ${cameras.length}`);
        console.log(`🎤 Microphones found: ${microphones.length}`);

        if (cameras.length === 0 && microphones.length > 0) {
            console.log('✅ Perfect setup for video-to-audio fallback!');
            console.log('📹 Video call button will automatically start audio calls');
            if (window.webrtcManager) {
                window.webrtcManager.showCallError('✅ Video calls will automatically become audio calls (no camera needed)', 'success');
            }
        } else if (cameras.length > 0 && microphones.length > 0) {
            console.log('✅ Full video calling available!');
            if (window.webrtcManager) {
                window.webrtcManager.showCallError('✅ Both audio and video calls available', 'success');
            }
        } else if (microphones.length === 0) {
            console.log('❌ No microphone found - calls not possible');
            if (window.webrtcManager) {
                window.webrtcManager.showCallError('❌ No microphone found - please connect a microphone', 'error');
            }
        }

        return {
            cameras: cameras.length,
            microphones: microphones.length,
            videoCallsAvailable: cameras.length > 0 && microphones.length > 0,
            audioCallsAvailable: microphones.length > 0,
            willFallbackToAudio: cameras.length === 0 && microphones.length > 0
        };

    } catch (error) {
        console.error('❌ Error testing fallback:', error);
        return null;
    }
}

// SUPER SIMPLE audio-only test - bypasses everything
async function testAudioOnly() {
    console.log('🧪 SUPER SIMPLE audio-only test...');

    try {
        // Most basic audio-only request possible
        const stream = await navigator.mediaDevices.getUserMedia({
            audio: true,
            video: false  // NEVER request video
        });

        console.log('✅ SUCCESS: Audio-only access works!');
        console.log('Stream tracks:', stream.getTracks().map(t => `${t.kind}: ${t.label}`));

        // Stop immediately
        stream.getTracks().forEach(track => track.stop());

        if (window.webrtcManager) {
            window.webrtcManager.showCallError('✅ Audio-only test SUCCESS! No camera needed.', 'success');
        }

        return true;

    } catch (error) {
        console.error('❌ FAILED: Audio-only test failed:', error);
        console.error('Error name:', error.name);
        console.error('Error message:', error.message);

        let message = `❌ Audio test failed: ${error.message}`;

        if (error.name === 'NotAllowedError') {
            message += '\n\n🔧 Fix: Click microphone icon in browser address bar → Allow';
        } else if (error.name === 'NotFoundError') {
            message += '\n\n🔧 Fix: Check microphone is connected and working';
        }

        if (window.webrtcManager) {
            window.webrtcManager.showCallError(message);
        }

        return false;
    }
}

// EMERGENCY AUDIO TEST - completely isolated
window.emergencyAudioTest = async function() {
    console.log('🚨 EMERGENCY AUDIO TEST - bypassing ALL systems');

    try {
        console.log('Step 1: Checking browser support...');
        if (!navigator.mediaDevices) {
            throw new Error('navigator.mediaDevices not available');
        }

        console.log('Step 2: Requesting audio-only access...');
        const stream = await navigator.mediaDevices.getUserMedia({
            audio: true,
            video: false
        });

        console.log('Step 3: SUCCESS! Got audio stream:', stream);
        console.log('Audio tracks:', stream.getAudioTracks().length);
        console.log('Video tracks:', stream.getVideoTracks().length);

        console.log('Step 4: Stopping stream...');
        stream.getTracks().forEach(track => track.stop());

        console.log('✅ EMERGENCY TEST PASSED - Audio access works!');
        alert('✅ SUCCESS: Audio access works perfectly!');
        return true;

    } catch (error) {
        console.error('❌ EMERGENCY TEST FAILED:', error);
        console.error('Error details:', {
            name: error.name,
            message: error.message,
            stack: error.stack
        });

        alert(`❌ FAILED: ${error.name} - ${error.message}`);
        return false;
    }
};

// Test microphone without server connection
window.testMicOnly = async function() {
    console.log('🎤 Testing microphone only (no server needed)...');

    try {
        const stream = await navigator.mediaDevices.getUserMedia({
            audio: true,
            video: false
        });

        console.log('✅ Microphone works!');
        console.log('Audio tracks:', stream.getAudioTracks().map(t => t.label));

        stream.getTracks().forEach(track => track.stop());

        alert('✅ SUCCESS: Microphone works perfectly!');
        return true;

    } catch (error) {
        console.error('❌ Microphone test failed:', error);
        alert(`❌ Microphone failed: ${error.name} - ${error.message}`);
        return false;
    }
};

// Check and fix socket connection
window.checkSocketConnection = function() {
    console.log('🔌 Checking socket connection...');
    console.log('Socket exists:', !!window.socket);
    console.log('Socket connected:', window.socket ? window.socket.connected : 'No socket');
    console.log('Socket ID:', window.socket ? window.socket.id : 'No socket');

    if (!window.socket) {
        console.log('❌ No socket found - page may not be fully loaded');
        alert('❌ No socket connection. Please refresh the page.');
        return false;
    }

    if (!window.socket.connected) {
        console.log('❌ Socket not connected - trying to reconnect...');
        window.socket.connect();

        setTimeout(() => {
            if (window.socket.connected) {
                console.log('✅ Socket reconnected successfully');
                alert('✅ Socket reconnected successfully');
            } else {
                console.log('❌ Socket reconnection failed');
                alert('❌ Socket reconnection failed. Please refresh the page.');
            }
        }, 2000);

        return false;
    }

    console.log('✅ Socket connection is working');
    alert('✅ Socket connection is working perfectly');
    return true;
};

// Test audio call setup without server connection
window.testAudioCallSetup = async function() {
    console.log('🧪 Testing audio call setup (no server needed)...');

    try {
        // Step 1: Test microphone access
        console.log('Step 1: Testing microphone access...');
        const stream = await navigator.mediaDevices.getUserMedia({
            audio: true,
            video: false
        });

        console.log('✅ Microphone access: SUCCESS');

        // Step 2: Test WebRTC peer connection
        console.log('Step 2: Testing WebRTC peer connection...');
        const pc = new RTCPeerConnection({
            iceServers: [{ urls: 'stun:stun.l.google.com:19302' }]
        });

        console.log('✅ WebRTC peer connection: SUCCESS');

        // Step 3: Add audio track
        console.log('Step 3: Adding audio track...');
        stream.getTracks().forEach(track => {
            pc.addTrack(track, stream);
            console.log(`Added ${track.kind} track: ${track.label}`);
        });

        console.log('✅ Audio track added: SUCCESS');

        // Step 4: Create offer (simulates call initiation)
        console.log('Step 4: Creating call offer...');
        const offer = await pc.createOffer();
        await pc.setLocalDescription(offer);

        console.log('✅ Call offer created: SUCCESS');

        // Cleanup
        stream.getTracks().forEach(track => track.stop());
        pc.close();

        console.log('✅ ALL TESTS PASSED - Audio calling is ready!');
        alert('✅ SUCCESS: Audio calling setup works perfectly!\n\nYour microphone and WebRTC are working.\nThe issue is just the server connection.');

        return true;

    } catch (error) {
        console.error('❌ Audio call setup failed:', error);
        alert(`❌ FAILED: ${error.name} - ${error.message}`);
        return false;
    }
};

// Debug function to check system status
function debugSystemStatus() {
    console.log('🔍 System Status Check:');
    console.log('='.repeat(50));
    console.log('WebRTC Manager:', !!window.webrtcManager);
    console.log('Socket Available:', !!window.socket);
    console.log('Socket Connected:', window.socket ? window.socket.connected : 'N/A');
    console.log('Socket ID:', window.socket ? window.socket.id : 'N/A');
    console.log('Current Contact ID:', window.currentContactId);
    console.log('Current Contact Name:', window.currentContactName);
    console.log('getUserMedia Support:', !!(navigator.mediaDevices && navigator.mediaDevices.getUserMedia));
    console.log('WebRTC Support:', !!window.RTCPeerConnection);
    console.log('Protocol:', location.protocol);
    console.log('Host:', location.host);
    console.log('='.repeat(50));

    // Test socket if available
    if (window.socket && window.socket.connected) {
        console.log('🧪 Testing socket connection...');
        testSocketConnection();
    }

    return {
        webrtcManager: !!window.webrtcManager,
        socket: !!window.socket,
        socketConnected: window.socket ? window.socket.connected : false,
        currentContactId: window.currentContactId,
        mediaSupport: !!(navigator.mediaDevices && navigator.mediaDevices.getUserMedia),
        webrtcSupport: !!window.RTCPeerConnection
    };
}

// Initialize WebRTC manager when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    if (typeof io !== 'undefined') {
        window.webrtcManager = new WebRTCCallManager();

        // Show helpful console messages
        console.log('%c🎤 Kawaii Chat - Voice & Video Calls Ready!', 'color: #ff6b9d; font-size: 16px; font-weight: bold;');
        console.log('%c🔧 TROUBLESHOOTING COMMANDS:', 'color: #e91e63; font-size: 14px; font-weight: bold;');
        console.log('%c  testAudioOnly()       - SIMPLEST audio test (try this first!)', 'color: #4caf50; font-size: 12px; font-weight: bold;');
        console.log('%c  testMicrophoneOnly()  - Test microphone with WebRTC manager', 'color: #4caf50; font-size: 12px;');
        console.log('%c  startAudioOnlyCall(1) - Test audio call to user ID 1', 'color: #2196f3; font-size: 12px;');
        console.log('%c  runFullDiagnostics()  - Complete system check', 'color: #e91e63; font-size: 12px;');
        console.log('%c  checkMediaDevices()   - Check microphone/camera availability', 'color: #ff5722; font-size: 12px;');
        console.log('%cOr visit: /media_test for detailed diagnostics', 'color: #666; font-size: 12px;');

        // Check if we're on localhost or HTTPS
        if (location.protocol !== 'https:' && location.hostname !== 'localhost' && location.hostname !== '127.0.0.1') {
            console.warn('%c⚠️ Media access requires HTTPS or localhost!', 'color: #ff9800; font-size: 14px; font-weight: bold;');
            console.log('%cCurrent URL: ' + location.href, 'color: #666;');
        }

    } else {
        console.error('❌ Socket.IO not loaded, WebRTC functionality disabled');
    }
});
