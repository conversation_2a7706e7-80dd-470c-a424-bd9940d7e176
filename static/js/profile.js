// Kawaii Chat - Profile Picture Upload

function showProfilePictureModal() {
    const modal = document.getElementById('profilePictureModal');
    if (modal) {
        modal.classList.add('show');
        resetProfilePictureModal();
    }
}

function closeProfilePictureModal() {
    const modal = document.getElementById('profilePictureModal');
    if (modal) {
        modal.classList.remove('show');
        resetProfilePictureModal();
    }
}

function resetProfilePictureModal() {
    const fileInput = document.getElementById('profilePictureInput');
    const preview = document.getElementById('profilePreview');
    const uploadBtn = document.getElementById('uploadProfileBtn');
    
    if (fileInput) fileInput.value = '';
    if (preview) preview.style.display = 'none';
    if (uploadBtn) {
        uploadBtn.disabled = true;
        uploadBtn.innerHTML = '<i class="fas fa-upload"></i> Upload Picture';
        uploadBtn.classList.remove('btn-loading');
    }
}

function handleProfilePictureSelect(event) {
    const file = event.target.files[0];
    if (!file) return;
    
    // Check file size (10MB)
    if (file.size > 10 * 1024 * 1024) {
        showToast('File too large! Max 10MB for profile pictures 📷', 'error');
        return;
    }
    
    // Check if it's an image
    if (!file.type.startsWith('image/')) {
        showToast('Please upload an image file! 🖼️', 'error');
        return;
    }
    
    // Show preview
    const preview = document.getElementById('profilePreview');
    const previewImage = document.getElementById('previewImage');
    const uploadBtn = document.getElementById('uploadProfileBtn');
    
    if (previewImage) {
        previewImage.src = URL.createObjectURL(file);
        previewImage.onload = function() {
            URL.revokeObjectURL(this.src); // Free memory
        };
    }
    
    if (preview) preview.style.display = 'block';
    if (uploadBtn) uploadBtn.disabled = false;
    
    // Create heart effect
    if (window.KawaiiEffects) {
        const effects = new window.KawaiiEffects();
        effects.createHeartBurst(event.target);
    }
}

async function uploadProfilePicture() {
    const fileInput = document.getElementById('profilePictureInput');
    const file = fileInput.files[0];
    
    if (!file) {
        showToast('Please select a file! 📷', 'error');
        return;
    }
    
    const uploadBtn = document.getElementById('uploadProfileBtn');
    uploadBtn.disabled = true;
    uploadBtn.classList.add('btn-loading');
    uploadBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Uploading...';
    
    try {
        const formData = new FormData();
        formData.append('file', file);
        
        const response = await fetch('/upload_profile_picture', {
            method: 'POST',
            body: formData
        });
        
        const result = await response.json();
        
        if (result.success) {
            // Update the profile avatar on the page
            updateProfileAvatar(result.profile_picture_url);
            
            closeProfilePictureModal();
            showToast(result.message, 'success');
            
            // Create confetti effect
            if (window.createConfetti) {
                window.createConfetti();
            }
            
            // Create heart effect
            if (window.KawaiiEffects) {
                const effects = new window.KawaiiEffects();
                effects.createHeartBurst(uploadBtn);
            }
        } else {
            showToast(result.message || 'Failed to upload profile picture 😢', 'error');
        }
    } catch (error) {
        console.error('Error uploading profile picture:', error);
        showToast('Error uploading profile picture 😢', 'error');
    } finally {
        uploadBtn.disabled = false;
        uploadBtn.classList.remove('btn-loading');
        uploadBtn.innerHTML = '<i class="fas fa-upload"></i> Upload Picture';
    }
}

function updateProfileAvatar(imageUrl) {
    // Update the main profile avatar
    const profileAvatar = document.querySelector('.profile-avatar');
    if (profileAvatar) {
        // Remove existing content
        profileAvatar.innerHTML = '';
        
        // Add new profile picture
        const img = document.createElement('img');
        img.src = imageUrl;
        img.alt = 'Profile Picture';
        img.className = 'profile-picture';
        profileAvatar.appendChild(img);
        
        // Keep the decoration
        const decoration = document.createElement('div');
        decoration.className = 'avatar-decoration';
        decoration.innerHTML = '✨';
        profileAvatar.appendChild(decoration);
    }
    
    // Update any other avatars on the page (like in chat sidebar)
    const userAvatars = document.querySelectorAll('.user-avatar');
    userAvatars.forEach(avatar => {
        avatar.innerHTML = '';
        const img = document.createElement('img');
        img.src = imageUrl;
        img.alt = 'Profile Picture';
        avatar.appendChild(img);
    });
}

function copyContactCode() {
    const contactCodeElement = document.querySelector('.contact-code');
    if (!contactCodeElement) return;
    
    const contactCode = contactCodeElement.textContent.trim();
    
    // Try to use the modern clipboard API
    if (navigator.clipboard && window.isSecureContext) {
        navigator.clipboard.writeText(contactCode).then(() => {
            showCopySuccess();
        }).catch(() => {
            fallbackCopyTextToClipboard(contactCode);
        });
    } else {
        fallbackCopyTextToClipboard(contactCode);
    }
}

function fallbackCopyTextToClipboard(text) {
    const textArea = document.createElement('textarea');
    textArea.value = text;
    textArea.style.position = 'fixed';
    textArea.style.left = '-999999px';
    textArea.style.top = '-999999px';
    document.body.appendChild(textArea);
    textArea.focus();
    textArea.select();
    
    try {
        document.execCommand('copy');
        showCopySuccess();
    } catch (err) {
        console.error('Fallback: Oops, unable to copy', err);
        showToast('Failed to copy contact code 😢', 'error');
    }
    
    document.body.removeChild(textArea);
}

function showCopySuccess() {
    const copyBtn = document.querySelector('.copy-btn');
    if (copyBtn) {
        const originalText = copyBtn.innerHTML;
        copyBtn.innerHTML = '<i class="fas fa-check"></i> Copied!';
        copyBtn.classList.add('success');
        
        setTimeout(() => {
            copyBtn.innerHTML = originalText;
            copyBtn.classList.remove('success');
        }, 2000);
    }
    
    showToast('Contact code copied! 📋✨', 'success');
    
    // Create heart effect
    if (window.KawaiiEffects) {
        const effects = new window.KawaiiEffects();
        effects.createHeartBurst(copyBtn || document.querySelector('.contact-code'));
    }
}

// Initialize profile page functionality
document.addEventListener('DOMContentLoaded', () => {
    // Add click handler for copy button
    const copyBtn = document.querySelector('.copy-btn');
    if (copyBtn) {
        copyBtn.addEventListener('click', copyContactCode);
    }
    
    // Add kawaii effects to profile elements
    if (window.KawaiiEffects) {
        const effects = new window.KawaiiEffects();
        
        // Add hover effects to stats
        const statItems = document.querySelectorAll('.stat-item');
        statItems.forEach(item => {
            item.addEventListener('mouseenter', () => {
                effects.createSparkles(item);
            });
        });
        
        // Add click effect to profile avatar
        const profileAvatar = document.querySelector('.profile-avatar');
        if (profileAvatar) {
            profileAvatar.addEventListener('click', () => {
                effects.createHeartBurst(profileAvatar);
            });
        }
    }
});

// Global functions for template
window.showProfilePictureModal = showProfilePictureModal;
window.closeProfilePictureModal = closeProfilePictureModal;
window.handleProfilePictureSelect = handleProfilePictureSelect;
window.uploadProfilePicture = uploadProfilePicture;
window.copyContactCode = copyContactCode;
