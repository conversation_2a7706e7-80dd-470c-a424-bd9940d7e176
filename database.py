"""
Database module for Kawaii Chat application.
Handles database connections, table creation, and database operations.
"""

import sqlite3
import psycopg2
from datetime import datetime
from config import Config

def get_db_connection():
    """Get database connection based on configuration."""
    if Config.USE_POSTGRES:
        return psycopg2.connect(Config.DATABASE)
    else:
        conn = sqlite3.connect(Config.DATABASE)
        conn.row_factory = sqlite3.Row
        return conn

def init_database():
    """Initialize database tables and perform migrations."""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        if Config.USE_POSTGRES:
            _create_postgres_tables(cursor)
        else:
            _create_sqlite_tables(cursor)
        
        # Perform migrations
        _perform_migrations(cursor)
        
        conn.commit()
        conn.close()
        print("✅ Database initialized successfully!")
        
    except Exception as e:
        print(f"❌ Database initialization error: {e}")

def _create_postgres_tables(cursor):
    """Create PostgreSQL tables."""
    # Users table
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS users (
            id SERIAL PRIMARY KEY,
            username VARCHAR(80) UNIQUE NOT NULL,
            password_hash VARCHAR(255) NOT NULL,
            contact_code VARCHAR(10) UNIQUE NOT NULL,
            avatar_color VARCHAR(7) DEFAULT '#ff6b9d',
            display_name VARCHAR(100),
            profile_picture_url TEXT,
            last_seen TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    ''')

    # Contacts table
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS contacts (
            id SERIAL PRIMARY KEY,
            user_id INTEGER NOT NULL,
            contact_user_id INTEGER NOT NULL,
            contact_name VARCHAR(100),
            added_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users (id),
            FOREIGN KEY (contact_user_id) REFERENCES users (id),
            UNIQUE(user_id, contact_user_id)
        )
    ''')

    # Messages table
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS messages (
            id SERIAL PRIMARY KEY,
            sender_id INTEGER NOT NULL,
            receiver_id INTEGER NOT NULL,
            message TEXT NOT NULL,
            sent_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            read_at TIMESTAMP,
            message_type VARCHAR(20) DEFAULT 'text',
            media_url TEXT,
            media_type VARCHAR(20),
            file_size INTEGER,
            reply_to_message_id INTEGER,
            call_status VARCHAR(20),
            call_duration INTEGER,
            FOREIGN KEY (sender_id) REFERENCES users (id),
            FOREIGN KEY (receiver_id) REFERENCES users (id),
            FOREIGN KEY (reply_to_message_id) REFERENCES messages (id)
        )
    ''')

def _create_sqlite_tables(cursor):
    """Create SQLite tables."""
    # Users table
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS users (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            username TEXT UNIQUE NOT NULL,
            password_hash TEXT NOT NULL,
            contact_code TEXT UNIQUE NOT NULL,
            avatar_color TEXT DEFAULT '#ff6b9d',
            display_name TEXT,
            profile_picture_url TEXT,
            last_seen DATETIME DEFAULT CURRENT_TIMESTAMP,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    ''')

    # Contacts table
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS contacts (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id INTEGER NOT NULL,
            contact_user_id INTEGER NOT NULL,
            contact_name TEXT,
            added_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users (id),
            FOREIGN KEY (contact_user_id) REFERENCES users (id),
            UNIQUE(user_id, contact_user_id)
        )
    ''')

    # Messages table
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS messages (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            sender_id INTEGER NOT NULL,
            receiver_id INTEGER NOT NULL,
            message TEXT NOT NULL,
            sent_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            read_at TIMESTAMP,
            message_type TEXT DEFAULT 'text',
            media_url TEXT,
            media_type TEXT,
            file_size INTEGER,
            reply_to_message_id INTEGER,
            call_status TEXT,
            call_duration INTEGER,
            FOREIGN KEY (sender_id) REFERENCES users (id),
            FOREIGN KEY (receiver_id) REFERENCES users (id),
            FOREIGN KEY (reply_to_message_id) REFERENCES messages (id)
        )
    ''')

def _perform_migrations(cursor):
    """Perform database migrations."""
    try:
        # Add message_reactions table
        if Config.USE_POSTGRES:
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS message_reactions (
                    id SERIAL PRIMARY KEY,
                    message_id INTEGER NOT NULL,
                    user_id INTEGER NOT NULL,
                    reaction VARCHAR(10) NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (message_id) REFERENCES messages (id),
                    FOREIGN KEY (user_id) REFERENCES users (id),
                    UNIQUE(message_id, user_id)
                )
            """)
        else:
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS message_reactions (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    message_id INTEGER NOT NULL,
                    user_id INTEGER NOT NULL,
                    reaction TEXT NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (message_id) REFERENCES messages (id),
                    FOREIGN KEY (user_id) REFERENCES users (id),
                    UNIQUE(message_id, user_id)
                )
            ''')
        
        # Add calls table
        if Config.USE_POSTGRES:
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS calls (
                    id SERIAL PRIMARY KEY,
                    caller_id INTEGER NOT NULL,
                    receiver_id INTEGER NOT NULL,
                    call_type VARCHAR(10) NOT NULL CHECK (call_type IN ('audio', 'video')),
                    call_status VARCHAR(20) NOT NULL DEFAULT 'initiated' CHECK (call_status IN ('initiated', 'ringing', 'accepted', 'rejected', 'ended', 'missed')),
                    started_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    answered_at TIMESTAMP,
                    ended_at TIMESTAMP,
                    duration INTEGER DEFAULT 0,
                    FOREIGN KEY (caller_id) REFERENCES users (id),
                    FOREIGN KEY (receiver_id) REFERENCES users (id)
                )
            ''')
        else:
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS calls (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    caller_id INTEGER NOT NULL,
                    receiver_id INTEGER NOT NULL,
                    call_type TEXT NOT NULL CHECK (call_type IN ('audio', 'video')),
                    call_status TEXT NOT NULL DEFAULT 'initiated' CHECK (call_status IN ('initiated', 'ringing', 'accepted', 'rejected', 'ended', 'missed')),
                    started_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    answered_at TIMESTAMP,
                    ended_at TIMESTAMP,
                    duration INTEGER DEFAULT 0,
                    FOREIGN KEY (caller_id) REFERENCES users (id),
                    FOREIGN KEY (receiver_id) REFERENCES users (id)
                )
            ''')
        
        print("✅ Database migrations completed!")
        
    except Exception as e:
        print(f"Migration error (this might be normal if tables already exist): {e}")

def get_user_by_id(user_id):
    """Get user by ID."""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        if Config.USE_POSTGRES:
            cursor.execute('SELECT * FROM users WHERE id = %s', (user_id,))
        else:
            cursor.execute('SELECT * FROM users WHERE id = ?', (user_id,))
        
        user = cursor.fetchone()
        conn.close()
        return user
    except Exception as e:
        print(f"Error getting user by ID: {e}")
        return None

def get_user_by_username(username):
    """Get user by username."""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        if Config.USE_POSTGRES:
            cursor.execute('SELECT * FROM users WHERE username = %s', (username,))
        else:
            cursor.execute('SELECT * FROM users WHERE username = ?', (username,))
        
        user = cursor.fetchone()
        conn.close()
        return user
    except Exception as e:
        print(f"Error getting user by username: {e}")
        return None

def update_user_last_seen(user_id):
    """Update user's last seen timestamp."""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        if Config.USE_POSTGRES:
            cursor.execute('UPDATE users SET last_seen = CURRENT_TIMESTAMP WHERE id = %s', (user_id,))
        else:
            cursor.execute('UPDATE users SET last_seen = datetime("now") WHERE id = ?', (user_id,))
        
        conn.commit()
        conn.close()
        print(f"📱 Updated last_seen for user {user_id}")
        
    except Exception as e:
        print(f"❌ Error updating last_seen for user {user_id}: {e}")
