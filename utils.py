"""
Utility functions for Kawaii Chat application.
Contains helper functions, file handling, and common utilities.
"""

import os
import uuid
import random
import string
import hashlib
import hmac
import time
from datetime import datetime
from werkzeug.utils import secure_filename
from PIL import Image
import io
import base64
import cloudinary
import cloudinary.uploader
import cloudinary.api
from config import Config

# Configure Cloudinary
cloudinary.config(
    cloud_name=Config.CLOUDINARY_CLOUD_NAME,
    api_key=Config.CLOUDINARY_API_KEY,
    api_secret=Config.CLOUDINARY_API_SECRET
)

def generate_contact_code():
    """Generate a unique 6-digit contact code."""
    return ''.join(random.choices(string.digits, k=6))

def generate_avatar_color():
    """Generate a random avatar color."""
    colors = [
        '#ff6b9d', '#c44569', '#f8b500', '#e55039', '#3c6382',
        '#0c2461', '#40407a', '#706fd3', '#f7f1e3', '#33d9b2',
        '#218c74', '#ff9ff3', '#54a0ff', '#5f27cd', '#00d2d3',
        '#ff9f43', '#feca57', '#ff6348', '#ff4757', '#c44569',
        '#a29bfe', '#6c5ce7', '#fd79a8', '#fdcb6e', '#e17055'
    ]
    return random.choice(colors)

def allowed_file(filename):
    """Check if file extension is allowed."""
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in Config.ALLOWED_EXTENSIONS

def get_file_type(filename):
    """Determine file type from filename."""
    ext = filename.lower().split('.')[-1] if '.' in filename else ''
    
    image_exts = ['jpg', 'jpeg', 'png', 'gif', 'webp', 'bmp']
    video_exts = ['mp4', 'avi', 'mov', 'wmv', 'flv', 'webm', 'mkv']
    audio_exts = ['mp3', 'wav', 'ogg', 'aac', 'flac', 'm4a']
    
    if ext in image_exts:
        return 'image'
    elif ext in video_exts:
        return 'video'
    elif ext in audio_exts:
        return 'audio'
    else:
        return 'document'

def upload_to_cloudinary(file, folder="kawaii_chat"):
    """Upload file to Cloudinary and return URL."""
    try:
        # Generate unique filename
        unique_filename = f"{folder}/{uuid.uuid4().hex}_{secure_filename(file.filename)}"
        
        # Upload to Cloudinary
        result = cloudinary.uploader.upload(
            file,
            public_id=unique_filename,
            resource_type="auto",  # Auto-detect file type
            folder=folder
        )
        
        return result['secure_url']
    except Exception as e:
        print(f"❌ Cloudinary upload error: {e}")
        return None

def process_profile_picture(file, user_id):
    """Process and upload profile picture."""
    try:
        # Open and process image
        image = Image.open(file.stream)
        
        # Convert to RGB if necessary
        if image.mode in ('RGBA', 'LA', 'P'):
            image = image.convert('RGB')
        
        # Resize to 200x200
        image = image.resize((200, 200), Image.Resampling.LANCZOS)
        
        # Save to bytes
        img_byte_arr = io.BytesIO()
        image.save(img_byte_arr, format='JPEG', quality=85)
        img_byte_arr.seek(0)
        
        # Generate unique filename
        filename = f"profile_{user_id}_{uuid.uuid4().hex}.jpg"
        
        # Upload to Cloudinary
        result = cloudinary.uploader.upload(
            img_byte_arr,
            public_id=f"kawaii_chat/profiles/{filename}",
            resource_type="image",
            folder="kawaii_chat/profiles"
        )
        
        return result['secure_url']
    except Exception as e:
        print(f"❌ Profile picture processing error: {e}")
        return None

def format_call_duration(duration_seconds):
    """Format call duration in a human-readable format."""
    if not duration_seconds:
        return "0:00"
    
    duration_seconds = int(duration_seconds)
    hours = duration_seconds // 3600
    minutes = (duration_seconds % 3600) // 60
    seconds = duration_seconds % 60
    
    if hours > 0:
        return f"{hours}:{minutes:02d}:{seconds:02d}"
    else:
        return f"{minutes}:{seconds:02d}"

def generate_signature(data, secret):
    """Generate HMAC signature for data validation."""
    return hmac.new(
        secret.encode('utf-8'),
        data.encode('utf-8'),
        hashlib.sha256
    ).hexdigest()

def verify_signature(data, signature, secret):
    """Verify HMAC signature."""
    expected_signature = generate_signature(data, secret)
    return hmac.compare_digest(signature, expected_signature)

def sanitize_filename(filename):
    """Sanitize filename for safe storage."""
    # Remove path components
    filename = os.path.basename(filename)
    # Secure the filename
    filename = secure_filename(filename)
    # Add timestamp to avoid conflicts
    name, ext = os.path.splitext(filename)
    timestamp = int(time.time())
    return f"{name}_{timestamp}{ext}"

def get_file_size_mb(file_size_bytes):
    """Convert file size from bytes to MB."""
    return round(file_size_bytes / (1024 * 1024), 2)

def validate_image_file(file):
    """Validate image file type and size."""
    if not file:
        return False, "No file provided"
    
    if not allowed_file(file.filename):
        return False, "File type not allowed"
    
    # Check file size (max 10MB for images)
    file.seek(0, os.SEEK_END)
    file_size = file.tell()
    file.seek(0)
    
    if file_size > 10 * 1024 * 1024:  # 10MB
        return False, "File too large (max 10MB)"
    
    # Try to open as image
    try:
        Image.open(file.stream)
        file.stream.seek(0)
        return True, "Valid image file"
    except Exception:
        return False, "Invalid image file"

def create_thumbnail(image_path, size=(150, 150)):
    """Create thumbnail for image."""
    try:
        with Image.open(image_path) as image:
            image.thumbnail(size, Image.Resampling.LANCZOS)
            
            # Save thumbnail
            thumbnail_path = image_path.replace('.', '_thumb.')
            image.save(thumbnail_path, quality=85)
            
            return thumbnail_path
    except Exception as e:
        print(f"❌ Thumbnail creation error: {e}")
        return None

def format_timestamp(timestamp):
    """Format timestamp for display."""
    if isinstance(timestamp, str):
        timestamp = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
    
    now = datetime.now()
    diff = now - timestamp
    
    if diff.days == 0:
        return timestamp.strftime('%H:%M')
    elif diff.days == 1:
        return 'Yesterday'
    elif diff.days < 7:
        return timestamp.strftime('%A')
    else:
        return timestamp.strftime('%d/%m/%Y')

def generate_unique_id():
    """Generate a unique ID."""
    return str(uuid.uuid4())

def clean_html(text):
    """Clean HTML from text (basic sanitization)."""
    import re
    # Remove HTML tags
    clean = re.compile('<.*?>')
    return re.sub(clean, '', text)

def truncate_text(text, max_length=100):
    """Truncate text to specified length."""
    if len(text) <= max_length:
        return text
    return text[:max_length-3] + '...'

def is_valid_contact_code(code):
    """Validate contact code format."""
    return code and len(code) == 6 and code.isdigit()

def get_mime_type(filename):
    """Get MIME type from filename."""
    ext = filename.lower().split('.')[-1] if '.' in filename else ''
    
    mime_types = {
        'jpg': 'image/jpeg',
        'jpeg': 'image/jpeg',
        'png': 'image/png',
        'gif': 'image/gif',
        'webp': 'image/webp',
        'mp4': 'video/mp4',
        'avi': 'video/avi',
        'mov': 'video/quicktime',
        'mp3': 'audio/mpeg',
        'wav': 'audio/wav',
        'ogg': 'audio/ogg',
        'pdf': 'application/pdf',
        'txt': 'text/plain'
    }
    
    return mime_types.get(ext, 'application/octet-stream')

def log_activity(user_id, action, details=None):
    """Log user activity (for debugging/monitoring)."""
    timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    log_entry = f"[{timestamp}] User {user_id}: {action}"
    if details:
        log_entry += f" - {details}"
    print(log_entry)
