<!DOCTYPE html>
<html>
<head>
    <title>Real-time Messaging Test</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.0.1/socket.io.js"></script>
</head>
<body>
    <h1>Real-time Messaging Test</h1>
    
    <div>
        <h3>Connection Status</h3>
        <div id="status">Disconnected</div>
    </div>
    
    <div>
        <h3>Send Message</h3>
        <input type="text" id="receiverId" placeholder="Receiver ID" value="2">
        <input type="text" id="messageText" placeholder="Message">
        <button onclick="sendMessage()">Send</button>
    </div>
    
    <div>
        <h3>Messages</h3>
        <div id="messages"></div>
    </div>
    
    <script>
        const socket = io();
        const statusDiv = document.getElementById('status');
        const messagesDiv = document.getElementById('messages');
        
        socket.on('connect', function() {
            statusDiv.textContent = 'Connected ✅';
            statusDiv.style.color = 'green';
            console.log('Connected to server');
        });
        
        socket.on('disconnect', function() {
            statusDiv.textContent = 'Disconnected ❌';
            statusDiv.style.color = 'red';
            console.log('Disconnected from server');
        });
        
        socket.on('connected', function(data) {
            console.log('Server welcome:', data);
            addMessage('System: ' + data.message);
        });
        
        socket.on('new_message', function(data) {
            console.log('New message received:', data);
            addMessage(`From ${data.sender_username}: ${data.message}`);
        });
        
        socket.on('message_sent', function(data) {
            console.log('Message sent confirmation:', data);
            addMessage(`Sent to ${data.receiver_id}: ${data.message}`);
        });
        
        function sendMessage() {
            const receiverId = document.getElementById('receiverId').value;
            const message = document.getElementById('messageText').value;
            
            if (!receiverId || !message) {
                alert('Please fill in both fields');
                return;
            }
            
            console.log('Sending message:', { receiver_id: receiverId, message: message });
            socket.emit('send_message', {
                receiver_id: receiverId,
                message: message
            });
            
            document.getElementById('messageText').value = '';
        }
        
        function addMessage(text) {
            const div = document.createElement('div');
            div.textContent = new Date().toLocaleTimeString() + ' - ' + text;
            messagesDiv.appendChild(div);
        }
        
        // Send message on Enter key
        document.getElementById('messageText').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                sendMessage();
            }
        });
    </script>
</body>
</html>
