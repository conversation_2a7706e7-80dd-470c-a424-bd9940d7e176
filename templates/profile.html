{% extends "base.html" %}

{% block title %}Profile - Kawaii Chat 🌸{% endblock %}

{% block content %}
<!-- Navigation Menu -->
<nav class="main-nav">
    <div class="nav-container">
        <div class="nav-brand">
            <i class="fas fa-comments"></i>
            <span>Chat App</span>
        </div>

        <button class="nav-toggle" id="navToggle" onclick="toggleNavMenu()">
            <span></span>
            <span></span>
            <span></span>
        </button>

        <div class="nav-menu" id="navMenu">
            <a href="{{ url_for('chat.chat_page') }}" class="nav-link">
                <i class="fas fa-comments"></i>
                <span>Chat</span>
            </a>
            <a href="{{ url_for('auth.profile') }}" class="nav-link active">
                <i class="fas fa-user-circle"></i>
                <span>Profile</span>
            </a>
            <a href="{{ url_for('auth.logout') }}" class="nav-link logout">
                <i class="fas fa-sign-out-alt"></i>
                <span>Logout</span>
            </a>
        </div>
    </div>
</nav>

<div class="profile-container">
    <!-- Header -->
    <header class="profile-header">
        <div class="header-content">
            <h1 class="page-title">
                <i class="fas fa-user-circle"></i>
                Your Kawaii Profile
                <i class="fas fa-sparkles"></i>
            </h1>
            <nav class="profile-nav">
                <a href="{{ url_for('chat.chat_page') }}" class="nav-btn">
                    <i class="fas fa-comments"></i>
                    Chat
                </a>
                <a href="{{ url_for('auth.profile') }}" class="nav-btn active">
                    <i class="fas fa-user-circle"></i>
                    Profile
                </a>
                <a href="{{ url_for('auth.logout') }}" class="nav-btn logout">
                    <i class="fas fa-sign-out-alt"></i>
                    Logout
                </a>
            </nav>
        </div>
    </header>
    
    <!-- Profile Card -->
    <div class="profile-card">
        <div class="profile-avatar-container">
            <div class="profile-avatar" style="background: {{ user[4] }}">
                {% if user[6] %}
                    <img src="{{ user[6] }}" alt="Profile Picture" class="profile-picture">
                {% else %}
                    <div class="avatar-icon">
                        <i class="fas fa-user"></i>
                    </div>
                {% endif %}
                <div class="avatar-decoration">✨</div>
            </div>
            <button class="change-avatar-btn" onclick="showProfilePictureModal()">
                <i class="fas fa-camera"></i>
                Change Photo
            </button>
        </div>
        
        <div class="profile-info">
            <h2 class="profile-username">{{ user[1] }}</h2>
            <p class="profile-joined">
                <i class="fas fa-calendar-alt"></i>
                Joined {{ user[7][:10] if user[7] else 'Recently' }}
            </p>
        </div>
        
        <!-- Contact Code Section -->
        <div class="contact-code-section">
            <h3 class="section-title">
                <i class="fas fa-qrcode"></i>
                Your Contact Code
            </h3>
            <div class="contact-code-display">
                <div class="contact-code" id="contactCode">{{ user[3] }}</div>
                <button class="copy-btn" onclick="copyContactCode()">
                    <i class="fas fa-copy"></i>
                    Copy
                </button>
            </div>
            <p class="contact-code-hint">
                Share this code with friends so they can add you! 💕
            </p>
        </div>
        
        <!-- Stats Section -->
        <div class="profile-stats">
            <div class="stat-item">
                <div class="stat-number" id="contactCount">0</div>
                <div class="stat-label">
                    <i class="fas fa-users"></i>
                    Contacts
                </div>
            </div>
            <div class="stat-item">
                <div class="stat-number" id="messageCount">0</div>
                <div class="stat-label">
                    <i class="fas fa-envelope"></i>
                    Messages
                </div>
            </div>
        </div>
        
        <!-- Quick Actions -->
        <div class="quick-actions">
            <button class="action-btn" onclick="generateNewCode()">
                <i class="fas fa-sync-alt"></i>
                Generate New Code
            </button>
            <button class="action-btn" onclick="shareProfile()">
                <i class="fas fa-share-alt"></i>
                Share Profile
            </button>
        </div>
    </div>
    
    <!-- Kawaii Decorations -->
    <div class="profile-decorations">
        <div class="decoration bounce-1">🌸</div>
        <div class="decoration bounce-2">🦄</div>
        <div class="decoration bounce-3">💖</div>
        <div class="decoration bounce-4">⭐</div>
        <div class="decoration bounce-5">🌈</div>
    </div>
</div>

<!-- Profile Picture Upload Modal -->
<div class="modal" id="profilePictureModal">
    <div class="modal-content">
        <div class="modal-header">
            <h3>
                <i class="fas fa-camera"></i>
                Change Profile Picture
            </h3>
            <button class="close-btn" onclick="closeProfilePictureModal()">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <div class="modal-body">
            <div class="upload-area" id="profileUploadArea">
                <div class="upload-zone" onclick="document.getElementById('profilePictureInput').click()">
                    <i class="fas fa-user-circle"></i>
                    <p>Click to select your kawaii profile picture</p>
                    <small>JPG, PNG, GIF (Max 10MB) - Will be cropped to 200x200</small>
                </div>
                <input type="file" id="profilePictureInput" accept="image/*" style="display: none;" onchange="handleProfilePictureSelect(event)">
            </div>
            <div class="profile-preview" id="profilePreview" style="display: none;">
                <div class="preview-container" id="profilePreviewContainer">
                    <div class="preview-avatar">
                        <img id="previewImage" src="" alt="Preview">
                    </div>
                </div>
                <p class="preview-note">
                    <i class="fas fa-info-circle"></i>
                    Your photo will be automatically cropped to fit perfectly! 📷✨
                </p>
            </div>
        </div>
        <div class="modal-footer">
            <button class="btn-secondary" onclick="closeProfilePictureModal()">Cancel</button>
            <button class="btn-primary" id="uploadProfileBtn" onclick="uploadProfilePicture()" disabled>
                <i class="fas fa-upload"></i>
                Upload Picture
            </button>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="{{ url_for('static', filename='js/profile.js') }}"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    loadProfileStats();
    animateDecorations();
});

function copyContactCode() {
    const contactCode = document.getElementById('contactCode').textContent;
    const copyBtn = document.querySelector('.copy-btn');
    
    navigator.clipboard.writeText(contactCode).then(() => {
        // Success animation
        copyBtn.innerHTML = '<i class="fas fa-check"></i> Copied!';
        copyBtn.classList.add('success');
        
        // Create heart burst
        createHeartBurst(copyBtn);
        
        // Reset after 2 seconds
        setTimeout(() => {
            copyBtn.innerHTML = '<i class="fas fa-copy"></i> Copy';
            copyBtn.classList.remove('success');
        }, 2000);
        
        // Show toast
        showToast('Contact code copied! Share it with friends! 💕', 'success');
    }).catch(() => {
        showToast('Failed to copy code 😢', 'error');
    });
}

function generateNewCode() {
    if (confirm('Are you sure? Your old contact code will stop working! 🤔')) {
        // This would need a backend endpoint to implement
        showToast('Feature coming soon! 🚧', 'info');
    }
}

function shareProfile() {
    const contactCode = document.getElementById('contactCode').textContent;
    const username = '{{ user[1] }}';
    const shareText = `Hey! Add me on Kawaii Chat! 🌸\nUsername: ${username}\nContact Code: ${contactCode}`;
    
    if (navigator.share) {
        navigator.share({
            title: 'Add me on Kawaii Chat!',
            text: shareText
        });
    } else {
        navigator.clipboard.writeText(shareText).then(() => {
            showToast('Profile info copied to clipboard! 📋', 'success');
        });
    }
}

function loadProfileStats() {
    // This would load actual stats from the backend
    // For now, showing placeholder animation
    animateNumber('contactCount', 0, 5, 1000);
    animateNumber('messageCount', 0, 42, 1500);
}

function animateNumber(elementId, start, end, duration) {
    const element = document.getElementById(elementId);
    const startTime = performance.now();
    
    function update(currentTime) {
        const elapsed = currentTime - startTime;
        const progress = Math.min(elapsed / duration, 1);
        const current = Math.floor(start + (end - start) * progress);
        
        element.textContent = current;
        
        if (progress < 1) {
            requestAnimationFrame(update);
        }
    }
    
    requestAnimationFrame(update);
}

function animateDecorations() {
    const decorations = document.querySelectorAll('.decoration');
    decorations.forEach((decoration, index) => {
        decoration.style.animationDelay = (index * 0.5) + 's';
    });
}

function createHeartBurst(element) {
    const hearts = ['💕', '💖', '💗', '💘', '💝', '💞'];
    const rect = element.getBoundingClientRect();
    
    for (let i = 0; i < 8; i++) {
        const heart = document.createElement('div');
        heart.className = 'heart-burst';
        heart.innerHTML = hearts[Math.floor(Math.random() * hearts.length)];
        
        const angle = (i / 8) * 2 * Math.PI;
        const distance = 50;
        const x = rect.left + rect.width/2 + Math.cos(angle) * distance;
        const y = rect.top + rect.height/2 + Math.sin(angle) * distance;
        
        heart.style.left = x + 'px';
        heart.style.top = y + 'px';
        
        document.body.appendChild(heart);
        
        setTimeout(() => {
            if (heart.parentNode) {
                heart.parentNode.removeChild(heart);
            }
        }, 1000);
    }
}

function showToast(message, type) {
    const toast = document.createElement('div');
    toast.className = `toast toast-${type}`;
    toast.innerHTML = `<i class="fas fa-heart"></i> ${message}`;
    
    document.body.appendChild(toast);
    
    setTimeout(() => toast.classList.add('show'), 100);
    
    setTimeout(() => {
        toast.classList.remove('show');
        setTimeout(() => {
            if (toast.parentNode) {
                toast.parentNode.removeChild(toast);
            }
        }, 300);
    }, 3000);
}

// Navigation Menu Toggle
const navToggle = document.getElementById('navToggle');
const navMenu = document.getElementById('navMenu');

if (navToggle && navMenu) {
    navToggle.addEventListener('click', function() {
        navToggle.classList.toggle('active');
        navMenu.classList.toggle('active');
    });

    // Close menu when clicking outside
    document.addEventListener('click', function(e) {
        if (!navToggle.contains(e.target) && !navMenu.contains(e.target)) {
            navToggle.classList.remove('active');
            navMenu.classList.remove('active');
        }
    });

    // Close menu when clicking on a link
    const navLinks = navMenu.querySelectorAll('.nav-link');
    navLinks.forEach(link => {
        link.addEventListener('click', function() {
            navToggle.classList.remove('active');
            navMenu.classList.remove('active');
        });
    });
}

// Simple backup function for nav toggle
function toggleNavMenu() {
    console.log('toggleNavMenu called');
    const navToggle = document.getElementById('navToggle');
    const navMenu = document.getElementById('navMenu');

    console.log('navToggle element:', navToggle);
    console.log('navMenu element:', navMenu);

    if (navToggle && navMenu) {
        const isActive = navMenu.classList.contains('active');
        console.log('Current state - active:', isActive);

        if (isActive) {
            navToggle.classList.remove('active');
            navMenu.classList.remove('active');
            console.log('Menu closed');
        } else {
            navToggle.classList.add('active');
            navMenu.classList.add('active');
            console.log('Menu opened');
        }

        // Force style update
        navMenu.style.transform = isActive ? 'translateY(-100%)' : 'translateY(0)';
        navMenu.style.opacity = isActive ? '0' : '1';
        navMenu.style.visibility = isActive ? 'hidden' : 'visible';

    } else {
        console.error('Nav elements not found');
        console.error('navToggle:', navToggle);
        console.error('navMenu:', navMenu);
    }
}
</script>
{% endblock %}
