{% extends "base.html" %}

{% block title %}Sign Up - Kawaii Chat 🌸{% endblock %}

{% block content %}
<div class="auth-container">
    <div class="auth-card">
        <!-- Kawaii Mascot -->
        <div class="kawaii-mascot">
            <div class="mascot-face excited">
                <div class="eyes">
                    <div class="eye left"></div>
                    <div class="eye right"></div>
                </div>
                <div class="mouth happy"></div>
                <div class="blush left-blush"></div>
                <div class="blush right-blush"></div>
            </div>
            <div class="mascot-text">Join the Fun! 🎀</div>
        </div>
        
        <!-- Signup Form -->
        <form method="POST" class="auth-form">
            <h1 class="auth-title">
                <i class="fas fa-sparkles"></i>
                Create Your Account
                <i class="fas fa-sparkles"></i>
            </h1>
            
            <div class="form-group">
                <label for="username">
                    <i class="fas fa-user"></i>
                    Username
                </label>
                <input type="text" id="username" name="username" required 
                       placeholder="Choose a kawaii username... 🦄"
                       minlength="3" maxlength="20">
                <small class="form-hint">3-20 characters, make it cute! ✨</small>
            </div>
            
            <div class="form-group">
                <label for="password">
                    <i class="fas fa-lock"></i>
                    Password
                </label>
                <input type="password" id="password" name="password" required 
                       placeholder="Create a strong password... 🔒"
                       minlength="6">
                <small class="form-hint">At least 6 characters for security! 🛡️</small>
            </div>
            
            <div class="form-group">
                <label for="confirm_password">
                    <i class="fas fa-lock"></i>
                    Confirm Password
                </label>
                <input type="password" id="confirm_password" name="confirm_password" required 
                       placeholder="Confirm your password... 💕">
                <small class="form-hint" id="password-match"></small>
            </div>
            
            <button type="submit" class="btn-primary">
                <i class="fas fa-user-plus"></i>
                Create Account
                <span class="btn-sparkle">🌟</span>
            </button>
        </form>
        
        <!-- Login Link -->
        <div class="auth-footer">
            <p>Already have an account? 💖</p>
            <a href="{{ url_for('auth.login') }}" class="auth-link">
                <i class="fas fa-sign-in-alt"></i>
                Login Here
            </a>
        </div>

        <!-- Navigation -->
        <div class="auth-nav">
            <a href="{{ url_for('auth.login') }}" class="auth-nav-btn">
                <i class="fas fa-sign-in-alt"></i>
                Login
            </a>
            <a href="{{ url_for('auth.register') }}" class="auth-nav-btn active">
                <i class="fas fa-user-plus"></i>
                Register
            </a>
        </div>
    </div>
    
    <!-- Decorative Elements -->
    <div class="auth-decorations">
        <div class="decoration star-1">⭐</div>
        <div class="decoration star-2">🌟</div>
        <div class="decoration heart-1">💖</div>
        <div class="decoration heart-2">💕</div>
        <div class="decoration unicorn">🦄</div>
        <div class="decoration rainbow">🌈</div>
        <div class="decoration cake">🎂</div>
        <div class="decoration gift">🎁</div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.querySelector('.auth-form');
    const inputs = form.querySelectorAll('input');
    const passwordInput = document.getElementById('password');
    const confirmPasswordInput = document.getElementById('confirm_password');
    const passwordMatchHint = document.getElementById('password-match');
    
    // Input focus animations
    inputs.forEach(input => {
        input.addEventListener('focus', function() {
            this.parentElement.classList.add('focused');
            createHeartBurst(this);
        });
        
        input.addEventListener('blur', function() {
            this.parentElement.classList.remove('focused');
        });
    });
    
    // Password matching validation
    function checkPasswordMatch() {
        const password = passwordInput.value;
        const confirmPassword = confirmPasswordInput.value;
        
        if (confirmPassword === '') {
            passwordMatchHint.textContent = '';
            passwordMatchHint.className = 'form-hint';
            return;
        }
        
        if (password === confirmPassword) {
            passwordMatchHint.textContent = 'Passwords match! 💕';
            passwordMatchHint.className = 'form-hint success';
        } else {
            passwordMatchHint.textContent = 'Passwords do not match! 😅';
            passwordMatchHint.className = 'form-hint error';
        }
    }
    
    passwordInput.addEventListener('input', checkPasswordMatch);
    confirmPasswordInput.addEventListener('input', checkPasswordMatch);
    
    // Animate mascot
    const mascot = document.querySelector('.mascot-face');
    setInterval(() => {
        mascot.classList.add('blink');
        setTimeout(() => mascot.classList.remove('blink'), 200);
    }, 2500);
    
    // Form submission
    form.addEventListener('submit', function(e) {
        const password = passwordInput.value;
        const confirmPassword = confirmPasswordInput.value;
        
        if (password !== confirmPassword) {
            e.preventDefault();
            createErrorAnimation();
            return;
        }
        
        const submitBtn = this.querySelector('.btn-primary');
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Creating Account... 🌸';
        submitBtn.disabled = true;
    });
});

function createHeartBurst(element) {
    const hearts = ['💕', '💖', '💗', '💘', '💝', '💞'];
    const rect = element.getBoundingClientRect();
    
    for (let i = 0; i < 5; i++) {
        const heart = document.createElement('div');
        heart.className = 'heart-burst';
        heart.innerHTML = hearts[Math.floor(Math.random() * hearts.length)];
        heart.style.left = (rect.left + Math.random() * rect.width) + 'px';
        heart.style.top = rect.top + 'px';
        
        document.body.appendChild(heart);
        
        setTimeout(() => {
            if (heart.parentNode) {
                heart.parentNode.removeChild(heart);
            }
        }, 1000);
    }
}

function createErrorAnimation() {
    const form = document.querySelector('.auth-form');
    form.classList.add('shake');
    setTimeout(() => form.classList.remove('shake'), 500);
}
</script>
{% endblock %}
