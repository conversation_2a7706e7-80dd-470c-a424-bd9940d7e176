<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Media Access Test - Kawai<PERSON>t</title>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
            padding: 2rem;
            min-height: 100vh;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            padding: 2rem;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 2rem 0;
            padding: 1rem;
            border: 2px solid #ff6b9d;
            border-radius: 15px;
        }
        button {
            background: linear-gradient(45deg, #ff6b9d, #c44569);
            color: white;
            border: none;
            padding: 1rem 2rem;
            border-radius: 25px;
            cursor: pointer;
            font-size: 1rem;
            margin: 0.5rem;
            transition: all 0.3s ease;
        }
        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(255, 107, 157, 0.3);
        }
        .result {
            margin: 1rem 0;
            padding: 1rem;
            border-radius: 10px;
            font-weight: bold;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        video {
            width: 100%;
            max-width: 400px;
            border-radius: 10px;
            margin: 1rem 0;
        }
        .debug-info {
            background: #f8f9fa;
            padding: 1rem;
            border-radius: 10px;
            font-family: monospace;
            font-size: 0.9rem;
            white-space: pre-wrap;
            margin: 1rem 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎤 Media Access Test - Kawaii Chat</h1>
        <p>This page helps diagnose microphone and camera access issues for video calling.</p>
        
        <div class="test-section">
            <h2>🔍 Browser Support Check</h2>
            <button onclick="checkBrowserSupport()">Check Browser Support</button>
            <div id="browserResult" class="result" style="display: none;"></div>
        </div>
        
        <div class="test-section">
            <h2>🎤 Microphone Test</h2>
            <button onclick="testMicrophone()">Test Microphone Access</button>
            <div id="micResult" class="result" style="display: none;"></div>
        </div>
        
        <div class="test-section">
            <h2>📷 Camera Test</h2>
            <button onclick="testCamera()">Test Camera Access</button>
            <div id="cameraResult" class="result" style="display: none;"></div>
            <video id="testVideo" style="display: none;" autoplay muted></video>
        </div>
        
        <div class="test-section">
            <h2>🔒 Permissions Check</h2>
            <button onclick="checkPermissions()">Check Current Permissions</button>
            <div id="permissionsResult" class="result" style="display: none;"></div>
        </div>
        
        <div class="test-section">
            <h2>🌐 Environment Info</h2>
            <button onclick="showEnvironmentInfo()">Show Environment Info</button>
            <div id="envInfo" class="debug-info" style="display: none;"></div>
        </div>
        
        <div class="test-section">
            <h2>📋 Debug Log</h2>
            <div id="debugLog" class="debug-info"></div>
        </div>
    </div>

    <script>
        let debugLog = [];
        
        function log(message) {
            const timestamp = new Date().toLocaleTimeString();
            const logMessage = `[${timestamp}] ${message}`;
            debugLog.push(logMessage);
            document.getElementById('debugLog').textContent = debugLog.join('\n');
            console.log(logMessage);
        }
        
        function showResult(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            element.textContent = message;
            element.className = `result ${type}`;
            element.style.display = 'block';
            log(`${type.toUpperCase()}: ${message}`);
        }
        
        function checkBrowserSupport() {
            log('Checking browser support...');
            
            let result = '';
            let type = 'success';
            
            // Check WebRTC support
            if (!window.RTCPeerConnection) {
                result += '❌ WebRTC not supported. ';
                type = 'error';
            } else {
                result += '✅ WebRTC supported. ';
            }
            
            // Check getUserMedia support
            if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
                result += '❌ getUserMedia not supported. ';
                type = 'error';
            } else {
                result += '✅ getUserMedia supported. ';
            }
            
            // Check if HTTPS
            if (location.protocol !== 'https:' && location.hostname !== 'localhost' && location.hostname !== '127.0.0.1') {
                result += '⚠️ HTTPS required for media access (except localhost). ';
                type = 'error';
            } else {
                result += '✅ Secure context available. ';
            }
            
            showResult('browserResult', result, type);
        }
        
        async function testMicrophone() {
            log('Testing microphone access...');
            
            try {
                const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
                log('Microphone access granted');
                
                // Stop the stream
                stream.getTracks().forEach(track => track.stop());
                
                showResult('micResult', '✅ Microphone access successful!', 'success');
            } catch (error) {
                log(`Microphone access failed: ${error.name} - ${error.message}`);
                
                let message = '❌ Microphone access failed: ';
                if (error.name === 'NotAllowedError') {
                    message += 'Permission denied. Click the microphone icon in your browser address bar and allow access.';
                } else if (error.name === 'NotFoundError') {
                    message += 'No microphone found. Check your device connections.';
                } else if (error.name === 'SecurityError') {
                    message += 'Security error. Use HTTPS or localhost.';
                } else {
                    message += error.message;
                }
                
                showResult('micResult', message, 'error');
            }
        }
        
        async function testCamera() {
            log('Testing camera access...');
            
            try {
                const stream = await navigator.mediaDevices.getUserMedia({ video: true });
                log('Camera access granted');
                
                // Show video
                const video = document.getElementById('testVideo');
                video.srcObject = stream;
                video.style.display = 'block';
                
                showResult('cameraResult', '✅ Camera access successful! Video should appear below.', 'success');
                
                // Stop after 5 seconds
                setTimeout(() => {
                    stream.getTracks().forEach(track => track.stop());
                    video.style.display = 'none';
                    log('Camera test stream stopped');
                }, 5000);
                
            } catch (error) {
                log(`Camera access failed: ${error.name} - ${error.message}`);
                
                let message = '❌ Camera access failed: ';
                if (error.name === 'NotAllowedError') {
                    message += 'Permission denied. Click the camera icon in your browser address bar and allow access.';
                } else if (error.name === 'NotFoundError') {
                    message += 'No camera found. Check your device connections.';
                } else if (error.name === 'SecurityError') {
                    message += 'Security error. Use HTTPS or localhost.';
                } else {
                    message += error.message;
                }
                
                showResult('cameraResult', message, 'error');
            }
        }
        
        async function checkPermissions() {
            log('Checking permissions...');
            
            if (!navigator.permissions) {
                showResult('permissionsResult', 'ℹ️ Permissions API not available in this browser', 'info');
                return;
            }
            
            try {
                const micPermission = await navigator.permissions.query({ name: 'microphone' });
                const cameraPermission = await navigator.permissions.query({ name: 'camera' });
                
                const result = `🎤 Microphone: ${micPermission.state}\n📷 Camera: ${cameraPermission.state}`;
                showResult('permissionsResult', result, 'info');
                
            } catch (error) {
                log(`Permission check failed: ${error.message}`);
                showResult('permissionsResult', `❌ Permission check failed: ${error.message}`, 'error');
            }
        }
        
        function showEnvironmentInfo() {
            log('Showing environment info...');
            
            const info = `
Browser: ${navigator.userAgent}
Protocol: ${location.protocol}
Host: ${location.host}
HTTPS: ${location.protocol === 'https:' ? 'Yes' : 'No'}
Localhost: ${location.hostname === 'localhost' || location.hostname === '127.0.0.1' ? 'Yes' : 'No'}
WebRTC: ${window.RTCPeerConnection ? 'Supported' : 'Not supported'}
getUserMedia: ${navigator.mediaDevices && navigator.mediaDevices.getUserMedia ? 'Supported' : 'Not supported'}
Permissions API: ${navigator.permissions ? 'Supported' : 'Not supported'}
            `.trim();
            
            document.getElementById('envInfo').textContent = info;
            document.getElementById('envInfo').style.display = 'block';
        }
        
        // Auto-run browser support check on load
        window.addEventListener('load', () => {
            log('Page loaded, running initial checks...');
            checkBrowserSupport();
        });
    </script>
</body>
</html>
