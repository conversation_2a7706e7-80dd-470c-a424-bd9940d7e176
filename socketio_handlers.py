"""
Socket.IO event handlers for Kawaii Chat application.
Handles real-time communication, calls, and status updates.
"""

from flask_socketio import emit, join_room, leave_room
from flask import session, request
from database import get_db_connection, update_user_last_seen
from config import Config
from datetime import datetime
import uuid

def register_socketio_handlers(socketio):
    """Register all Socket.IO event handlers."""
    
    @socketio.on('connect')
    def handle_connect():
        """Handle client connection."""
        if 'user_id' not in session:
            print("❌ Connection rejected: No user_id in session")
            return False

        user_room = f"user_{session['user_id']}"
        join_room(user_room)
        print(f"✅ User {session['user_id']} connected and joined room: {user_room}")
        print(f"✅ Session ID: {request.sid}")
        emit('connected', {'message': 'Connected to Kawaii Chat! 🌸'})

        # Update user's last seen to now (they're online)
        update_user_last_seen(session['user_id'])
        
        # Broadcast user online status to their contacts
        print(f"🔄 About to broadcast ONLINE status for user {session['user_id']}")
        broadcast_user_status(session['user_id'], 'online')

    @socketio.on('disconnect')
    def handle_disconnect():
        """Handle client disconnection."""
        if 'user_id' in session:
            user_room = f"user_{session['user_id']}"
            leave_room(user_room)
            print(f"❌ User {session['user_id']} disconnected from room: {user_room}")
            
            # Update user's last seen to now (when they went offline)
            update_user_last_seen(session['user_id'])
            
            # Broadcast user offline status to their contacts
            print(f"🔄 About to broadcast OFFLINE status for user {session['user_id']}")
            broadcast_user_status(session['user_id'], 'offline')

    @socketio.on('send_message')
    def handle_send_message(data):
        """Handle real-time message sending."""
        if 'user_id' not in session:
            return

        receiver_id = data.get('receiver_id')
        message = data.get('message', '').strip()
        message_id = data.get('message_id')

        if not receiver_id or not message:
            return

        # Send message to receiver's room
        receiver_room = f"user_{receiver_id}"
        socketio.emit('new_message', {
            'message_id': message_id,
            'sender_id': session['user_id'],
            'sender_username': session.get('username', 'Unknown'),
            'sender_color': session.get('avatar_color', '#ff6b9d'),
            'message': message,
            'sent_at': datetime.now().isoformat()
        }, room=receiver_room)

        print(f"📨 Message sent: {session['user_id']} -> {receiver_id}")

    @socketio.on('message_read')
    def handle_message_read(data):
        """Handle message read confirmation."""
        if 'user_id' not in session:
            return

        message_id = data.get('message_id')
        sender_id = data.get('sender_id')

        if not message_id or not sender_id:
            return

        # Update message as read in database
        try:
            conn = get_db_connection()
            cursor = conn.cursor()
            
            if Config.USE_POSTGRES:
                cursor.execute('''
                    UPDATE messages 
                    SET read_at = CURRENT_TIMESTAMP 
                    WHERE id = %s AND receiver_id = %s
                ''', (message_id, session['user_id']))
            else:
                cursor.execute('''
                    UPDATE messages 
                    SET read_at = datetime('now') 
                    WHERE id = ? AND receiver_id = ?
                ''', (message_id, session['user_id']))
            
            conn.commit()
            conn.close()

            # Notify sender that message was read
            sender_room = f"user_{sender_id}"
            socketio.emit('message_read_confirmation', {
                'message_id': message_id,
                'read_by': session['user_id'],
                'read_at': datetime.now().isoformat()
            }, room=sender_room)

            print(f"✅ Message {message_id} marked as read by user {session['user_id']}")

        except Exception as e:
            print(f"❌ Error marking message as read: {e}")

    @socketio.on('initiate_call')
    def handle_initiate_call(data):
        """Handle call initiation."""
        if 'user_id' not in session:
            return

        receiver_id = data.get('receiver_id')
        call_type = data.get('call_type', 'audio')

        if not receiver_id:
            return

        try:
            # Create call record in database
            conn = get_db_connection()
            cursor = conn.cursor()
            
            if Config.USE_POSTGRES:
                cursor.execute('''
                    INSERT INTO calls (caller_id, receiver_id, call_type, call_status)
                    VALUES (%s, %s, %s, 'initiated')
                    RETURNING id
                ''', (session['user_id'], receiver_id, call_type))
                call_id = cursor.fetchone()[0]
            else:
                cursor.execute('''
                    INSERT INTO calls (caller_id, receiver_id, call_type, call_status)
                    VALUES (?, ?, ?, 'initiated')
                ''', (session['user_id'], receiver_id, call_type))
                call_id = cursor.lastrowid
            
            conn.commit()
            conn.close()

            # Send call invitation to receiver
            receiver_room = f"user_{receiver_id}"
            socketio.emit('incoming_call', {
                'call_id': call_id,
                'caller_id': session['user_id'],
                'caller_username': session.get('username', 'Unknown'),
                'call_type': call_type
            }, room=receiver_room)

            # Confirm call initiation to caller
            emit('call_initiated', {
                'call_id': call_id,
                'receiver_id': receiver_id,
                'call_type': call_type
            })

            print(f"📞 Call initiated: {session['user_id']} -> {receiver_id} ({call_type})")

        except Exception as e:
            print(f"❌ Error initiating call: {e}")
            emit('call_error', {'message': 'Failed to initiate call'})

    @socketio.on('answer_call')
    def handle_answer_call(data):
        """Handle call answer."""
        if 'user_id' not in session:
            return

        call_id = data.get('call_id')
        caller_id = data.get('caller_id')

        if not call_id or not caller_id:
            return

        try:
            # Update call status in database
            conn = get_db_connection()
            cursor = conn.cursor()
            
            if Config.USE_POSTGRES:
                cursor.execute('''
                    UPDATE calls 
                    SET call_status = 'accepted', answered_at = CURRENT_TIMESTAMP
                    WHERE id = %s AND receiver_id = %s
                ''', (call_id, session['user_id']))
            else:
                cursor.execute('''
                    UPDATE calls 
                    SET call_status = 'accepted', answered_at = datetime('now')
                    WHERE id = ? AND receiver_id = ?
                ''', (call_id, session['user_id']))
            
            conn.commit()
            conn.close()

            # Notify caller that call was answered
            caller_room = f"user_{caller_id}"
            socketio.emit('call_answered', {
                'call_id': call_id,
                'answered_by': session['user_id']
            }, room=caller_room)

            print(f"✅ Call answered: {call_id}")

        except Exception as e:
            print(f"❌ Error answering call: {e}")

    @socketio.on('reject_call')
    def handle_reject_call(data):
        """Handle call rejection."""
        if 'user_id' not in session:
            return

        call_id = data.get('call_id')
        caller_id = data.get('caller_id')

        if not call_id or not caller_id:
            return

        try:
            # Update call status in database
            conn = get_db_connection()
            cursor = conn.cursor()
            
            if Config.USE_POSTGRES:
                cursor.execute('''
                    UPDATE calls 
                    SET call_status = 'rejected', ended_at = CURRENT_TIMESTAMP
                    WHERE id = %s AND receiver_id = %s
                ''', (call_id, session['user_id']))
            else:
                cursor.execute('''
                    UPDATE calls 
                    SET call_status = 'rejected', ended_at = datetime('now')
                    WHERE id = ? AND receiver_id = ?
                ''', (call_id, session['user_id']))
            
            conn.commit()
            conn.close()

            # Notify caller that call was rejected
            caller_room = f"user_{caller_id}"
            socketio.emit('call_rejected', {
                'call_id': call_id,
                'rejected_by': session['user_id']
            }, room=caller_room)

            print(f"❌ Call rejected: {call_id}")

        except Exception as e:
            print(f"❌ Error rejecting call: {e}")

    @socketio.on('end_call')
    def handle_end_call(data):
        """Handle call ending."""
        if 'user_id' not in session:
            return

        call_id = data.get('call_id')
        other_user_id = data.get('other_user_id')
        duration = data.get('duration', 0)

        if not call_id:
            return

        try:
            # Update call status in database
            conn = get_db_connection()
            cursor = conn.cursor()
            
            if Config.USE_POSTGRES:
                cursor.execute('''
                    UPDATE calls 
                    SET call_status = 'ended', ended_at = CURRENT_TIMESTAMP, duration = %s
                    WHERE id = %s
                ''', (duration, call_id))
            else:
                cursor.execute('''
                    UPDATE calls 
                    SET call_status = 'ended', ended_at = datetime('now'), duration = ?
                    WHERE id = ?
                ''', (duration, call_id))
            
            conn.commit()
            conn.close()

            # Notify other user that call ended
            if other_user_id:
                other_room = f"user_{other_user_id}"
                socketio.emit('call_ended', {
                    'call_id': call_id,
                    'ended_by': session['user_id'],
                    'duration': duration
                }, room=other_room)

            print(f"📞 Call ended: {call_id} (duration: {duration}s)")

        except Exception as e:
            print(f"❌ Error ending call: {e}")

    @socketio.on('webrtc_offer')
    def handle_webrtc_offer(data):
        """Handle WebRTC offer exchange."""
        if 'user_id' not in session:
            return

        call_id = data.get('call_id')
        target_user_id = data.get('target_user_id')
        offer = data.get('offer')

        if not all([call_id, target_user_id, offer]):
            return

        # Forward offer to target user
        target_room = f"user_{target_user_id}"
        socketio.emit('webrtc_offer', {
            'call_id': call_id,
            'from_user_id': session['user_id'],
            'offer': offer
        }, room=target_room)

        print(f"📡 WebRTC offer forwarded: {call_id}")

    @socketio.on('webrtc_answer')
    def handle_webrtc_answer(data):
        """Handle WebRTC answer exchange."""
        if 'user_id' not in session:
            return

        call_id = data.get('call_id')
        target_user_id = data.get('target_user_id')
        answer = data.get('answer')

        if not all([call_id, target_user_id, answer]):
            return

        # Forward answer to target user
        target_room = f"user_{target_user_id}"
        socketio.emit('webrtc_answer', {
            'call_id': call_id,
            'from_user_id': session['user_id'],
            'answer': answer
        }, room=target_room)

        print(f"📡 WebRTC answer forwarded: {call_id}")

    @socketio.on('webrtc_ice_candidate')
    def handle_webrtc_ice_candidate(data):
        """Handle WebRTC ICE candidate exchange."""
        if 'user_id' not in session:
            return

        call_id = data.get('call_id')
        target_user_id = data.get('target_user_id')
        candidate = data.get('candidate')

        if not all([call_id, target_user_id, candidate]):
            return

        # Forward ICE candidate to target user
        target_room = f"user_{target_user_id}"
        socketio.emit('webrtc_ice_candidate', {
            'call_id': call_id,
            'from_user_id': session['user_id'],
            'candidate': candidate
        }, room=target_room)

        print(f"🧊 ICE candidate forwarded: {call_id}")

    @socketio.on('test_audio_stream')
    def handle_test_audio_stream(data):
        """Handle audio stream test request."""
        if 'user_id' not in session:
            return
        
        print(f"🎤 Audio stream test from user {session['user_id']}")
        
        # Echo back to confirm server received the test
        emit('audio_stream_test_response', {
            'status': 'success',
            'message': 'Audio stream test received by server',
            'user_id': session['user_id'],
            'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        })
        
        print(f"✅ Audio stream test response sent to user {session['user_id']}")

def broadcast_user_status(user_id, status):
    """Broadcast user online/offline status to their contacts with last_seen info."""
    try:
        # Get user's contacts and last_seen info
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # Get user's last_seen timestamp
        if Config.USE_POSTGRES:
            cursor.execute('SELECT last_seen FROM users WHERE id = %s', (user_id,))
        else:
            cursor.execute('SELECT last_seen FROM users WHERE id = ?', (user_id,))
        
        user_result = cursor.fetchone()
        last_seen = user_result[0] if user_result else None
        
        # Get user's contacts
        if Config.USE_POSTGRES:
            cursor.execute('''
                SELECT DISTINCT contact_user_id as contact_id
                FROM contacts 
                WHERE user_id = %s
                UNION
                SELECT DISTINCT user_id as contact_id
                FROM contacts 
                WHERE contact_user_id = %s
            ''', (user_id, user_id))
        else:
            cursor.execute('''
                SELECT DISTINCT contact_user_id as contact_id
                FROM contacts 
                WHERE user_id = ?
                UNION
                SELECT DISTINCT user_id as contact_id
                FROM contacts 
                WHERE contact_user_id = ?
            ''', (user_id, user_id))
        
        contacts = cursor.fetchall()
        conn.close()
        
        # Import socketio from flask_socketio to avoid circular imports
        from flask_socketio import emit

        # Broadcast status to each contact
        for (contact_id,) in contacts:
            contact_room = f"user_{contact_id}"
            emit('user_status_changed', {
                'user_id': user_id,
                'status': status,
                'last_seen': last_seen
            }, room=contact_room)
            
        print(f"📡 Broadcasted {status} status for user {user_id} to {len(contacts)} contacts (last_seen: {last_seen})")
        
    except Exception as e:
        print(f"❌ Error broadcasting user status: {e}")
