"""
Kawaii Chat - A cute and modern chat application
Built with Flask, Socket.IO, and lots of love! 🌸

Professional modular architecture with clean separation of concerns.
"""

from flask import Flask
from flask_socketio import Socket<PERSON>
from config import get_config
from database import init_database
from auth import auth_bp
from routes.chat import chat_bp
from socketio_handlers import register_socketio_handlers

def create_app():
    """Application factory pattern."""
    app = Flask(__name__)
    
    # Load configuration
    config_class = get_config()
    app.config.from_object(config_class)
    config_class.init_app(app)
    
    # Initialize extensions
    socketio = SocketIO(app, cors_allowed_origins=app.config['SOCKETIO_CORS_ALLOWED_ORIGINS'])
    
    # Initialize database
    init_database()
    
    # Register blueprints
    app.register_blueprint(auth_bp)
    app.register_blueprint(chat_bp)
    
    # Register Socket.IO handlers
    register_socketio_handlers(socketio)
    
    # Store socketio instance for use in other modules
    app.socketio = socketio
    
    return app, socketio

# Create application instance
app, socketio = create_app()

if __name__ == '__main__':
    print("🌸 Starting Kawaii Chat Server...")
    print("📱 Professional modular architecture loaded!")
    print("🚀 Server running on http://localhost:5000")
    
    # Run the application
    socketio.run(
        app,
        debug=True,
        host='0.0.0.0',
        port=5000,
        allow_unsafe_werkzeug=True
    )
