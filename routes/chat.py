"""
Chat routes module for Kawaii Chat application.
Handles chat-related routes, messaging, contacts, and file uploads.
"""

from flask import Blueprint, render_template, request, redirect, url_for, flash, session, jsonify, send_file
from werkzeug.utils import secure_filename
from database import get_db_connection, get_user_by_id
from utils import (
    upload_to_cloudinary, allowed_file, get_file_type, format_call_duration,
    sanitize_filename, get_file_size_mb, validate_image_file, is_valid_contact_code
)
from auth import require_login
from config import Config
import os
from datetime import datetime

# Create chat blueprint
chat_bp = Blueprint('chat', __name__)

@chat_bp.route('/chat')
@require_login
def chat_page():
    """Main chat page."""
    try:
        # Get user's contacts with last_seen info
        conn = get_db_connection()
        cursor = conn.cursor()
        
        if Config.USE_POSTGRES:
            cursor.execute('''
                SELECT u.id, u.username, u.avatar_color, c.contact_name, u.contact_code, u.profile_picture_url, u.last_seen
                FROM contacts c
                JOIN users u ON c.contact_user_id = u.id
                WHERE c.user_id = %s
                ORDER BY c.added_at DESC
            ''', (session['user_id'],))
        else:
            cursor.execute('''
                SELECT u.id, u.username, u.avatar_color, c.contact_name, u.contact_code, u.profile_picture_url, u.last_seen
                FROM contacts c
                JOIN users u ON c.contact_user_id = u.id
                WHERE c.user_id = ?
                ORDER BY c.added_at DESC
            ''', (session['user_id'],))
        
        contacts = cursor.fetchall()
        conn.close()
        
        return render_template('chat.html', contacts=contacts)
        
    except Exception as e:
        print(f"Chat page error: {e}")
        flash('Error loading chat page', 'error')
        return redirect(url_for('auth.login'))

@chat_bp.route('/add_contact', methods=['POST'])
@require_login
def add_contact():
    """Add a new contact by contact code."""
    contact_code = request.form.get('contact_code', '').strip()
    contact_name = request.form.get('contact_name', '').strip()
    
    if not contact_code:
        flash('Please enter a contact code', 'error')
        return redirect(url_for('chat.chat_page'))
    
    if not is_valid_contact_code(contact_code):
        flash('Invalid contact code format', 'error')
        return redirect(url_for('chat.chat_page'))
    
    if contact_code == session.get('contact_code'):
        flash('You cannot add yourself as a contact', 'error')
        return redirect(url_for('chat.chat_page'))
    
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # Find user by contact code
        if Config.USE_POSTGRES:
            cursor.execute('SELECT id, username FROM users WHERE contact_code = %s', (contact_code,))
        else:
            cursor.execute('SELECT id, username FROM users WHERE contact_code = ?', (contact_code,))
        
        user = cursor.fetchone()
        if not user:
            flash('Contact code not found', 'error')
            conn.close()
            return redirect(url_for('chat.chat_page'))
        
        contact_user_id = user[0]
        contact_username = user[1]
        
        # Check if contact already exists
        if Config.USE_POSTGRES:
            cursor.execute('''
                SELECT id FROM contacts 
                WHERE user_id = %s AND contact_user_id = %s
            ''', (session['user_id'], contact_user_id))
        else:
            cursor.execute('''
                SELECT id FROM contacts 
                WHERE user_id = ? AND contact_user_id = ?
            ''', (session['user_id'], contact_user_id))
        
        if cursor.fetchone():
            flash('Contact already exists', 'warning')
            conn.close()
            return redirect(url_for('chat.chat_page'))
        
        # Add contact
        final_contact_name = contact_name if contact_name else contact_username
        
        if Config.USE_POSTGRES:
            cursor.execute('''
                INSERT INTO contacts (user_id, contact_user_id, contact_name)
                VALUES (%s, %s, %s)
            ''', (session['user_id'], contact_user_id, final_contact_name))
        else:
            cursor.execute('''
                INSERT INTO contacts (user_id, contact_user_id, contact_name)
                VALUES (?, ?, ?)
            ''', (session['user_id'], contact_user_id, final_contact_name))
        
        conn.commit()
        conn.close()
        
        flash(f'Contact "{final_contact_name}" added successfully! 🌸', 'success')
        
    except Exception as e:
        print(f"Add contact error: {e}")
        flash('Failed to add contact', 'error')
    
    return redirect(url_for('chat.chat_page'))

@chat_bp.route('/send_message', methods=['POST'])
@require_login
def send_message():
    """Send a message to a contact."""
    try:
        receiver_id = request.form.get('receiver_id')
        message = request.form.get('message', '').strip()
        reply_to_message_id = request.form.get('reply_to_message_id')
        
        if not receiver_id or not message:
            return jsonify({'success': False, 'message': 'Missing required fields'}), 400
        
        if len(message) > Config.MAX_MESSAGE_LENGTH:
            return jsonify({'success': False, 'message': 'Message too long'}), 400
        
        # Insert message into database
        conn = get_db_connection()
        cursor = conn.cursor()
        
        if Config.USE_POSTGRES:
            cursor.execute('''
                INSERT INTO messages (sender_id, receiver_id, message, reply_to_message_id)
                VALUES (%s, %s, %s, %s)
                RETURNING id, sent_at
            ''', (session['user_id'], receiver_id, message, reply_to_message_id if reply_to_message_id else None))
            result = cursor.fetchone()
            message_id = result[0]
            sent_at = result[1]
        else:
            cursor.execute('''
                INSERT INTO messages (sender_id, receiver_id, message, reply_to_message_id)
                VALUES (?, ?, ?, ?)
            ''', (session['user_id'], receiver_id, message, reply_to_message_id if reply_to_message_id else None))
            message_id = cursor.lastrowid
            cursor.execute('SELECT sent_at FROM messages WHERE id = ?', (message_id,))
            sent_at = cursor.fetchone()[0]
        
        conn.commit()
        conn.close()
        
        return jsonify({
            'success': True,
            'message_id': message_id,
            'sent_at': sent_at.isoformat() if hasattr(sent_at, 'isoformat') else str(sent_at)
        })
        
    except Exception as e:
        print(f"Send message error: {e}")
        return jsonify({'success': False, 'message': 'Failed to send message'}), 500

@chat_bp.route('/get_messages/<int:contact_id>')
@require_login
def get_messages(contact_id):
    """Get messages with a specific contact."""
    try:
        page = request.args.get('page', 1, type=int)
        limit = min(request.args.get('limit', 50, type=int), Config.MAX_MESSAGES_PER_REQUEST)
        offset = (page - 1) * limit
        
        conn = get_db_connection()
        cursor = conn.cursor()
        
        if Config.USE_POSTGRES:
            cursor.execute('''
                SELECT m.id, m.sender_id, m.receiver_id, m.message, m.sent_at, m.read_at,
                       m.message_type, m.media_url, m.media_type, m.file_size,
                       m.reply_to_message_id, m.call_status, m.call_duration,
                       u.username as sender_username, u.avatar_color as sender_color
                FROM messages m
                JOIN users u ON m.sender_id = u.id
                WHERE (m.sender_id = %s AND m.receiver_id = %s) 
                   OR (m.sender_id = %s AND m.receiver_id = %s)
                ORDER BY m.sent_at DESC
                LIMIT %s OFFSET %s
            ''', (session['user_id'], contact_id, contact_id, session['user_id'], limit, offset))
        else:
            cursor.execute('''
                SELECT m.id, m.sender_id, m.receiver_id, m.message, m.sent_at, m.read_at,
                       m.message_type, m.media_url, m.media_type, m.file_size,
                       m.reply_to_message_id, m.call_status, m.call_duration,
                       u.username as sender_username, u.avatar_color as sender_color
                FROM messages m
                JOIN users u ON m.sender_id = u.id
                WHERE (m.sender_id = ? AND m.receiver_id = ?) 
                   OR (m.sender_id = ? AND m.receiver_id = ?)
                ORDER BY m.sent_at DESC
                LIMIT ? OFFSET ?
            ''', (session['user_id'], contact_id, contact_id, session['user_id'], limit, offset))
        
        messages = cursor.fetchall()
        conn.close()
        
        # Convert to list of dictionaries
        message_list = []
        for msg in messages:
            message_dict = {
                'id': msg[0],
                'sender_id': msg[1],
                'receiver_id': msg[2],
                'message': msg[3],
                'sent_at': msg[4].isoformat() if hasattr(msg[4], 'isoformat') else str(msg[4]),
                'read_at': msg[5].isoformat() if msg[5] and hasattr(msg[5], 'isoformat') else str(msg[5]) if msg[5] else None,
                'message_type': msg[6],
                'media_url': msg[7],
                'media_type': msg[8],
                'file_size': msg[9],
                'reply_to_message_id': msg[10],
                'call_status': msg[11],
                'call_duration': format_call_duration(msg[12]) if msg[12] else None,
                'sender_username': msg[13],
                'sender_color': msg[14]
            }
            message_list.append(message_dict)
        
        return jsonify({
            'success': True,
            'messages': list(reversed(message_list)),  # Reverse to show oldest first
            'page': page,
            'has_more': len(messages) == limit
        })
        
    except Exception as e:
        print(f"Get messages error: {e}")
        return jsonify({'success': False, 'message': 'Failed to load messages'}), 500

@chat_bp.route('/upload_file', methods=['POST'])
@require_login
def upload_file():
    """Upload a file and send as message."""
    try:
        if 'file' not in request.files:
            return jsonify({'success': False, 'message': 'No file selected'}), 400
        
        file = request.files['file']
        receiver_id = request.form.get('receiver_id')
        
        if not file or file.filename == '':
            return jsonify({'success': False, 'message': 'No file selected'}), 400
        
        if not receiver_id:
            return jsonify({'success': False, 'message': 'No receiver specified'}), 400
        
        if not allowed_file(file.filename):
            return jsonify({'success': False, 'message': 'File type not allowed'}), 400
        
        # Check file size
        file.seek(0, os.SEEK_END)
        file_size = file.tell()
        file.seek(0)
        
        if file_size > Config.MAX_CONTENT_LENGTH:
            return jsonify({'success': False, 'message': 'File too large'}), 400
        
        # Upload to Cloudinary
        media_url = upload_to_cloudinary(file)
        if not media_url:
            return jsonify({'success': False, 'message': 'Upload failed'}), 500
        
        # Determine file type and create message
        file_type = get_file_type(file.filename)
        message_text = f"📎 {file.filename}"
        
        # Insert message into database
        conn = get_db_connection()
        cursor = conn.cursor()
        
        if Config.USE_POSTGRES:
            cursor.execute('''
                INSERT INTO messages (sender_id, receiver_id, message, message_type, media_url, media_type, file_size)
                VALUES (%s, %s, %s, %s, %s, %s, %s)
                RETURNING id, sent_at
            ''', (session['user_id'], receiver_id, message_text, file_type, media_url, file_type, file_size))
            result = cursor.fetchone()
            message_id = result[0]
            sent_at = result[1]
        else:
            cursor.execute('''
                INSERT INTO messages (sender_id, receiver_id, message, message_type, media_url, media_type, file_size)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ''', (session['user_id'], receiver_id, message_text, file_type, media_url, file_type, file_size))
            message_id = cursor.lastrowid
            cursor.execute('SELECT sent_at FROM messages WHERE id = ?', (message_id,))
            sent_at = cursor.fetchone()[0]
        
        conn.commit()
        conn.close()
        
        return jsonify({
            'success': True,
            'message_id': message_id,
            'media_url': media_url,
            'file_type': file_type,
            'file_size': get_file_size_mb(file_size),
            'sent_at': sent_at.isoformat() if hasattr(sent_at, 'isoformat') else str(sent_at)
        })
        
    except Exception as e:
        print(f"File upload error: {e}")
        return jsonify({'success': False, 'message': 'Upload failed'}), 500

@chat_bp.route('/mark_messages_read', methods=['POST'])
@require_login
def mark_messages_read():
    """Mark messages as read."""
    try:
        contact_id = request.json.get('contact_id')
        if not contact_id:
            return jsonify({'success': False, 'message': 'Contact ID required'}), 400
        
        conn = get_db_connection()
        cursor = conn.cursor()
        
        if Config.USE_POSTGRES:
            cursor.execute('''
                UPDATE messages 
                SET read_at = CURRENT_TIMESTAMP 
                WHERE sender_id = %s AND receiver_id = %s AND read_at IS NULL
            ''', (contact_id, session['user_id']))
        else:
            cursor.execute('''
                UPDATE messages 
                SET read_at = datetime('now') 
                WHERE sender_id = ? AND receiver_id = ? AND read_at IS NULL
            ''', (contact_id, session['user_id']))
        
        conn.commit()
        conn.close()
        
        return jsonify({'success': True})
        
    except Exception as e:
        print(f"Mark messages read error: {e}")
        return jsonify({'success': False, 'message': 'Failed to mark messages as read'}), 500
