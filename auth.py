"""
Authentication module for Kawaii Chat application.
Handles user registration, login, logout, and session management.
"""

from flask import Blueprint, render_template, request, redirect, url_for, flash, session, jsonify
from werkzeug.security import generate_password_hash, check_password_hash
from database import get_db_connection, get_user_by_username
from utils import generate_contact_code, generate_avatar_color, process_profile_picture, is_valid_contact_code
from config import Config
import random

# Create authentication blueprint
auth_bp = Blueprint('auth', __name__)

@auth_bp.route('/')
def index():
    """Home page - redirect to chat if logged in, otherwise to login."""
    if 'user_id' in session:
        return redirect(url_for('chat.chat_page'))
    return redirect(url_for('auth.login'))

@auth_bp.route('/login', methods=['GET', 'POST'])
def login():
    """User login page and handler."""
    if request.method == 'POST':
        username = request.form.get('username', '').strip()
        password = request.form.get('password', '')
        
        if not username or not password:
            flash('Please fill in all fields', 'error')
            return render_template('login.html')
        
        # Get user from database
        user = get_user_by_username(username)
        
        if user and check_password_hash(user[2], password):  # user[2] is password_hash
            # Login successful
            session['user_id'] = user[0]  # user[0] is id
            session['username'] = user[1]  # user[1] is username
            session['contact_code'] = user[3]  # user[3] is contact_code
            session['avatar_color'] = user[4] if len(user) > 4 else '#ff6b9d'
            session['display_name'] = user[5] if len(user) > 5 else username
            
            flash(f'Welcome back, {username}! 🌸', 'success')
            return redirect(url_for('chat.chat_page'))
        else:
            flash('Invalid username or password', 'error')
    
    return render_template('login.html')

@auth_bp.route('/register', methods=['GET', 'POST'])
def register():
    """User registration page and handler."""
    if request.method == 'POST':
        username = request.form.get('username', '').strip()
        password = request.form.get('password', '')
        confirm_password = request.form.get('confirm_password', '')
        display_name = request.form.get('display_name', '').strip()
        
        # Validation
        if not username or not password:
            flash('Username and password are required', 'error')
            return render_template('signup.html')

        if len(username) < 3:
            flash('Username must be at least 3 characters long', 'error')
            return render_template('signup.html')

        if len(password) < 6:
            flash('Password must be at least 6 characters long', 'error')
            return render_template('signup.html')

        if password != confirm_password:
            flash('Passwords do not match', 'error')
            return render_template('signup.html')

        # Check if username already exists
        if get_user_by_username(username):
            flash('Username already exists', 'error')
            return render_template('signup.html')

        # Generate unique contact code
        contact_code = _generate_unique_contact_code()
        if not contact_code:
            flash('Registration failed. Please try again.', 'error')
            return render_template('signup.html')
        
        # Create user
        try:
            conn = get_db_connection()
            cursor = conn.cursor()
            
            password_hash = generate_password_hash(password)
            avatar_color = generate_avatar_color()
            final_display_name = display_name if display_name else username
            
            if Config.USE_POSTGRES:
                cursor.execute('''
                    INSERT INTO users (username, password_hash, contact_code, avatar_color, display_name)
                    VALUES (%s, %s, %s, %s, %s)
                    RETURNING id
                ''', (username, password_hash, contact_code, avatar_color, final_display_name))
                user_id = cursor.fetchone()[0]
            else:
                cursor.execute('''
                    INSERT INTO users (username, password_hash, contact_code, avatar_color, display_name)
                    VALUES (?, ?, ?, ?, ?)
                ''', (username, password_hash, contact_code, avatar_color, final_display_name))
                user_id = cursor.lastrowid
            
            conn.commit()
            conn.close()
            
            # Auto-login after registration
            session['user_id'] = user_id
            session['username'] = username
            session['contact_code'] = contact_code
            session['avatar_color'] = avatar_color
            session['display_name'] = final_display_name
            
            flash(f'Welcome to Kawaii Chat, {username}! Your contact code is: {contact_code} 🌸', 'success')
            return redirect(url_for('chat.chat_page'))
            
        except Exception as e:
            print(f"Registration error: {e}")
            flash('Registration failed. Please try again.', 'error')
    
    return render_template('signup.html')

@auth_bp.route('/logout')
def logout():
    """User logout."""
    username = session.get('username', 'User')
    session.clear()
    flash(f'Goodbye, {username}! See you soon! 👋', 'info')
    return redirect(url_for('auth.login'))

@auth_bp.route('/profile', methods=['GET', 'POST'])
def profile():
    """User profile page."""
    if 'user_id' not in session:
        return redirect(url_for('auth.login'))
    
    if request.method == 'POST':
        return _handle_profile_update()
    
    # Get user data
    conn = get_db_connection()
    cursor = conn.cursor()
    
    if Config.USE_POSTGRES:
        cursor.execute('SELECT * FROM users WHERE id = %s', (session['user_id'],))
    else:
        cursor.execute('SELECT * FROM users WHERE id = ?', (session['user_id'],))
    
    user = cursor.fetchone()
    conn.close()
    
    if not user:
        flash('User not found', 'error')
        return redirect(url_for('auth.login'))
    
    return render_template('profile.html', user=user)

@auth_bp.route('/upload_profile_picture', methods=['POST'])
def upload_profile_picture():
    """Handle profile picture upload."""
    if 'user_id' not in session:
        return jsonify({'success': False, 'message': 'Not logged in'}), 401
    
    if 'profile_picture' not in request.files:
        return jsonify({'success': False, 'message': 'No file selected'}), 400
    
    file = request.files['profile_picture']
    if file.filename == '':
        return jsonify({'success': False, 'message': 'No file selected'}), 400
    
    try:
        # Process and upload profile picture
        profile_url = process_profile_picture(file, session['user_id'])
        
        if not profile_url:
            return jsonify({'success': False, 'message': 'Failed to upload image'}), 500
        
        # Update user profile in database
        conn = get_db_connection()
        cursor = conn.cursor()
        
        if Config.USE_POSTGRES:
            cursor.execute(
                'UPDATE users SET profile_picture_url = %s WHERE id = %s',
                (profile_url, session['user_id'])
            )
        else:
            cursor.execute(
                'UPDATE users SET profile_picture_url = ? WHERE id = ?',
                (profile_url, session['user_id'])
            )
        
        conn.commit()
        conn.close()
        
        return jsonify({
            'success': True,
            'message': 'Profile picture updated successfully! 🌸',
            'profile_url': profile_url
        })
        
    except Exception as e:
        print(f"Profile picture upload error: {e}")
        return jsonify({'success': False, 'message': 'Upload failed'}), 500

def _generate_unique_contact_code():
    """Generate a unique contact code."""
    max_attempts = 100
    
    for _ in range(max_attempts):
        code = generate_contact_code()
        
        # Check if code already exists
        conn = get_db_connection()
        cursor = conn.cursor()
        
        if Config.USE_POSTGRES:
            cursor.execute('SELECT id FROM users WHERE contact_code = %s', (code,))
        else:
            cursor.execute('SELECT id FROM users WHERE contact_code = ?', (code,))
        
        if not cursor.fetchone():
            conn.close()
            return code
        
        conn.close()
    
    return None  # Failed to generate unique code

def _handle_profile_update():
    """Handle profile update form submission."""
    display_name = request.form.get('display_name', '').strip()
    current_password = request.form.get('current_password', '')
    new_password = request.form.get('new_password', '')
    confirm_password = request.form.get('confirm_password', '')
    
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # Update display name
        if display_name:
            if Config.USE_POSTGRES:
                cursor.execute(
                    'UPDATE users SET display_name = %s WHERE id = %s',
                    (display_name, session['user_id'])
                )
            else:
                cursor.execute(
                    'UPDATE users SET display_name = ? WHERE id = ?',
                    (display_name, session['user_id'])
                )
            session['display_name'] = display_name
        
        # Update password if provided
        if current_password and new_password:
            # Verify current password
            if Config.USE_POSTGRES:
                cursor.execute('SELECT password_hash FROM users WHERE id = %s', (session['user_id'],))
            else:
                cursor.execute('SELECT password_hash FROM users WHERE id = ?', (session['user_id'],))
            
            user_data = cursor.fetchone()
            if not user_data or not check_password_hash(user_data[0], current_password):
                flash('Current password is incorrect', 'error')
                conn.close()
                return redirect(url_for('auth.profile'))
            
            if len(new_password) < 6:
                flash('New password must be at least 6 characters long', 'error')
                conn.close()
                return redirect(url_for('auth.profile'))
            
            if new_password != confirm_password:
                flash('New passwords do not match', 'error')
                conn.close()
                return redirect(url_for('auth.profile'))
            
            # Update password
            new_password_hash = generate_password_hash(new_password)
            if Config.USE_POSTGRES:
                cursor.execute(
                    'UPDATE users SET password_hash = %s WHERE id = %s',
                    (new_password_hash, session['user_id'])
                )
            else:
                cursor.execute(
                    'UPDATE users SET password_hash = ? WHERE id = ?',
                    (new_password_hash, session['user_id'])
                )
        
        conn.commit()
        conn.close()
        
        flash('Profile updated successfully! 🌸', 'success')
        
    except Exception as e:
        print(f"Profile update error: {e}")
        flash('Profile update failed', 'error')
    
    return redirect(url_for('auth.profile'))

def require_login(f):
    """Decorator to require login for routes."""
    from functools import wraps
    
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if 'user_id' not in session:
            return redirect(url_for('auth.login'))
        return f(*args, **kwargs)
    return decorated_function
