"""
Configuration module for Kawaii Chat application.
Handles environment variables, app settings, and configuration management.
"""

import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

class Config:
    """Base configuration class."""
    
    # Flask Configuration
    SECRET_KEY = os.getenv('SECRET_KEY', 'kawaii-chat-secret-key-2024')
    MAX_CONTENT_LENGTH = 50 * 1024 * 1024  # 50MB max file size
    
    # Database Configuration
    DATABASE_URL = os.getenv('DATABASE_URL')
    USE_POSTGRES = DATABASE_URL and DATABASE_URL.startswith('postgresql://')
    DATABASE = DATABASE_URL if USE_POSTGRES else 'kawaii_chat.db'
    
    # Cloudinary Configuration
    CLOUDINARY_CLOUD_NAME = os.getenv('CLOUDINARY_CLOUD_NAME')
    CLOUDINARY_API_KEY = os.getenv('CLOUDINARY_API_KEY')
    CLOUDINARY_API_SECRET = os.getenv('CLOUDINARY_API_SECRET')
    
    # Socket.IO Configuration
    SOCKETIO_CORS_ALLOWED_ORIGINS = "*"
    
    # File Upload Configuration
    UPLOAD_FOLDER = 'uploads'
    ALLOWED_EXTENSIONS = {'txt', 'pdf', 'png', 'jpg', 'jpeg', 'gif', 'mp4', 'avi', 'mov', 'mp3', 'wav'}
    
    # Call Configuration
    CALL_TIMEOUT_SECONDS = 30
    MAX_CALL_DURATION_HOURS = 2
    
    # Chat Configuration
    MAX_MESSAGE_LENGTH = 5000
    MAX_MESSAGES_PER_REQUEST = 100
    
    @staticmethod
    def init_app(app):
        """Initialize application with configuration."""
        pass

class DevelopmentConfig(Config):
    """Development configuration."""
    DEBUG = True
    TESTING = False

class ProductionConfig(Config):
    """Production configuration."""
    DEBUG = False
    TESTING = False
    
    @staticmethod
    def init_app(app):
        Config.init_app(app)
        
        # Log to stderr in production
        import logging
        from logging import StreamHandler
        file_handler = StreamHandler()
        file_handler.setLevel(logging.INFO)
        app.logger.addHandler(file_handler)

class TestingConfig(Config):
    """Testing configuration."""
    TESTING = True
    DATABASE = ':memory:'  # Use in-memory database for testing
    WTF_CSRF_ENABLED = False

# Configuration dictionary
config = {
    'development': DevelopmentConfig,
    'production': ProductionConfig,
    'testing': TestingConfig,
    'default': DevelopmentConfig
}

def get_config():
    """Get configuration based on environment."""
    return config[os.getenv('FLASK_ENV', 'default')]
